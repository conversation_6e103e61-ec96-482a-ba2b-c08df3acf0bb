<!DOCTYPE html>
<html>
 <head> 
  	{include file="public/include"}
  <title>找回密码 - 个人中心 - {$maccms.site_name}</title> 
  <meta name="keywords" content="个人中心,{$maccms.site_keywords}" /> 
  <meta name="description" content="{$maccms.site_description}" />
  {include file="user/include"}
 </head>
 <body class="mxui-min-width">
 <div class="page list">
      {include file="public/head"} 
   <div class="mxui-part-case mxui-part-margin main"> 
    <div class="mxui-back-whits mxui-part-height" style="min-height:35rem"> 
     <div class="mxui-user-login mxui-part-core-bg"> 
      <form class="mxui-user-form mxui-user-width mxui-part-rows" method="post" id="fm" action=""> 
	   <input type="hidden" name="ac" value="{$param['ac']}">
       <div class="mxui-list-head mxui-part-rows mxui-padding"> 
        <h2 class="mxui-font-xvii mxui-text-center">{$param['ac_text']}找回密码</h2> 
       </div> 
       <input class="mxui-user-text mxui-col-xs12" type="text" id="to" name="to" placeholder="请输入{$param['ac_text']}" /> 
       <input class="mxui-user-text mxui-col-xs5" type="text" id="verify" name="verify" placeholder="请输入验证码" maxlength="4" /> 
       <img class="mxui-user-code mxui-user-text mxui-col-xs4 mxui-yzm" height="45" src="{:mac_url('verify/index')}" onclick="this.src=this.src+'?'" title="看不清楚? 换一张！" /> 
       <span class="mxui-user-tips mxui-text-gules mxui-padding-v mxui-visible mxui-col-xs12">　</span> 
       <input class="mxui-subm-finds mxui-user-submit mxui-rims-info mxui-btns-info mxui-btns-green mxui-col-xs12" type="button" id="btn_send" value="发送验证码" /> 
      </form> 
      <form class="mxui-user-form mxui-user-width mxui-part-rows" method="post" id="fm2" action=""> 
	   <input type="hidden" name="ac" value="{$param['ac']}">
       <div class="mxui-list-head mxui-part-rows mxui-padding"> 
        <h2 class="mxui-font-xvii mxui-text-center">验证信息</h2> 
       </div> 
       <input class="mxui-user-text mxui-col-xs12" type="text" id="code" name="code" placeholder="请输入验证码" /> 
       <input class="mxui-user-text mxui-col-xs12" type="password" id="user_pwd" name="user_pwd" placeholder="请输入新密码" maxlength="20" /> 
       <input class="mxui-user-text mxui-col-xs12" type="password" id="user_pwd2" name="user_pwd2" placeholder="再次输入新密码" maxlength="20" /> 
       <input class="mxui-subm-finds mxui-user-submit mxui-rims-info mxui-btns-info mxui-btns-green mxui-col-xs12 m20" type="button" id="btn_submit" value="重置密码" /> 
       <a class="mxui-col-xs3 mxui-text-left" href="{:mac_url('user/login')}">立即登录</a> 
       <a class="mxui-col-xs3 mxui-text-center" href="{:mac_url('user/findpass')}">问题找回</a> 
       <a class="mxui-col-xs3 mxui-text-center" href="{:mac_url('user/findpass_msg')}?ac=email">邮箱找回</a> 
       <a class="mxui-text-right" style="float:right" href="{:mac_url('user/findpass_msg')}?ac=phone">手机找回</a>
      </form> 
     </div> 
    </div> 
   </div> 
  </div>
  {include file="public/foot"}  
  <script>
	$(function(){
		$("body").bind('keyup',function(event) {
			if(event.keyCode==13){ $('#btnLogin').click(); }
		});
		$('#btn_send').click(function() {
			if ($('#to').val()  == '') { layer.msg('请输入{$param["ac_text"]}！'); $("#to").focus(); return false; }

			$.ajax({
				url: "{:mac_url('user/findpass_msg')}",
				type: "post",
				dataType: "json",
				data: $('#fm').serialize(),
				beforeSend: function () {
					$("#btn_send").val("loading...");
				},
				success: function (r) {
					layer.msg(r.msg);
				},
				complete: function () {
					$('#verify').click();
					$("#btn_send").val("发送邮件");
				}
			});
		});

		$('#btn_submit').click(function() {
			if ($('#to').val()  == '') { layer.msg('请输入{$param["ac_text"]}'); $("#to").focus(); return false; }
			if ($('#code').val()  == '') { layer.msg('请输入验证码！'); $("#code").focus(); return false; }
			if ($('#user_pwd').val()  == '') { layer.msg('请输入新密码！'); $("#user_pwd").focus(); return false; }
			if ($('#user_pwd2').val()  == '') { layer.msg('请输入确认密码！'); $("#user_pwd2").focus(); return false; }
			if ($('#user_pwd').val()  != $('#user_pwd2').val() ) { layer.msg('二次密码不一致！'); $("#user_pwd2").focus(); return false; }

			var data= {ac:'{$param["ac"]}',to:$('#to').val(),code:$('#code').val(),user_pwd:$('#user_pwd').val(),user_pwd2:$('#user_pwd2').val()};
			$.ajax({
				url: "{:mac_url('user/findpass_reset')}",
				type: "post",
				dataType: "json",
				data: data,
				beforeSend: function () {
					$("#btn_submit").val("loading...");
				},
				success: function (r) {
					layer.msg(r.msg);
				},
				complete: function () {
					$("#btn_submit").val("重置密码");
				}
			});
		});
	});
</script>  
 </body>
</html>