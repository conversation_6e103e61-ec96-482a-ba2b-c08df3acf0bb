<div class="pop-content">
<div class="popup popup-report open" id="report-popup">
  <!--<div class="popup-icon"><img src="{$maccms.path}mxstatic/picture/report.svg"></div>-->
  <div class="popup-header">
    <h2 class="popup-title">我要报错</h2>
  </div>
  <div class="popup-main">
    <div class="report-box">
      <form class="gbook_form">
        <input type="hidden" name="gbook_rid" value="{$param.id}">
        <p>如遇影片无法播放，可能存在网络拥堵等情况，可尝试<strong>切换节点</strong>或<strong>刷新</strong>页面。</p>
        <p>尝试后以下影片仍无法播放：</p>
        <textarea class="report-content" name="gbook_content">{$param.name}</textarea>
            {if condition="$gbook.verify eq 1"}
            <p class="verify-box"><input class="report-content report-input" name="verify" type="text" autocomplete="off">
             <img class="report-verify" src="{:mac_url('verify/index')}" onClick="this.src+='?'" ></p>
            {/if}
          
        <input type="button" class="gbook_submit popup-btn" value="确认无误，提交">
      </form>
    </div>
  </div>
      <div class="close-popup"><i class="icon icon-close"></i></div>
</div>
<script>
    $(function(){
        MAC.Gbook.Login = {$gbook.login};
        MAC.Gbook.Verify = {$gbook.verify};
        MAC.Gbook.Init();
    });
     $(".close-popup").click(function () {
     $(".pop-content").remove();
  });
</script>
<div class="shortcuts-mobile-overlay"></div>
</div>