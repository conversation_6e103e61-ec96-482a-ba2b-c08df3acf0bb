<!DOCTYPE html>
<html>
 <head> 
   	{include file="public/include"}
  <title>绑定数据 - 个人中心 - {$maccms.site_name}</title> 
  <meta name="keywords" content="个人中心,{$maccms.site_keywords}" /> 
  <meta name="description" content="{$maccms.site_description}" />
  {include file="user/include"}   
 </head>
 <body class="mxui-min-width">
   <div class="page list">
      {include file="public/head"} 
   <div class="mxui-part-case main"> 
    <div class="mxui-user-head mxui-margin-top mxui-back-whits"> 
     <div class="mxui-list-pics mxui-lazy mxui-part-5by2 mxui-part-rows" style="background:url({:mac_url_img($mxprost.mxprocms.s1.userbg)});"> 
      <div class="mxui-part-core mxui-text-center"> 
       <div class="mxui-user-image" data-role="{:mac_url('user/portrait')}"> 
        <img class="face mxui-user-avat mxui-part-roun" src="{$obj.user_portrait|mac_default='static/images/touxiang.png'|mac_url_img}?v={:time()}" /> 
       </div> 
       <span class="mxui-visible mxui-text-white mxui-padding">{$obj.user_name}</span> 
      </div> 
     </div> 
     <div class="mxui-padding mxui-part-rows mxui-back-whits mxui-hide-md"> 
      <ul class="mxui-user-brief mxui-part-rows mxui-back-whits"> 
       <li class="mxui-padding-x mxui-text-center mxui-col-xs4"> <span class="mxui-visible mxui-text-gules">{$obj.user_points}</span> <span class="mxui-visible">我的积分</span> </li> 
       <li class="mxui-padding-x mxui-text-center mxui-line-left mxui-col-xs4"> <span class="mxui-visible mxui-text-gules">{$obj.group.group_name}</span> <span class="mxui-visible">我的等级</span> </li> 
       <li class="mxui-padding-x mxui-text-center mxui-line-left mxui-col-xs4"> <span class="mxui-visible mxui-text-gules">{$obj.user_login_num}</span> <span class="mxui-visible">登录次数</span> </li> 
      </ul> 
     </div> 
    </div> 
    <div class="mxui-part-rows"> 
     <div class="mxui-main-left mxui-col-xs12 mxui-col-md4 mxui-col-lg3 mxui-hide-xs mxui-hide-sm mxui-show-md-block">
       {include file="user/menu"} 
     </div> 
     <div class="mxui-main-right mxui-col-xs12 mxui-col-md8 mxui-col-lg9"> 
      <div class="mxui-part-layout mxui-back-whits"> 
       <div class="mxui-user-title mxui-list-head mxui-part-rows mxui-padding mxui-line-bottom"> 
        <h2 class="mxui-font-xvi mxui-padding">绑定{if$ac=='phone'}手机{else}邮箱{/if}</h2> 
        <ul class="mxui-part-tips mxui-padding"> 
         <li class="mxui-padding"> <a class="mxui-more" href="{:mac_url('user/index')}">返回</a> </li> 
        </ul> 
       </div> 
       <form class="mxui-user-form mxui-user-info mxui-part-rows" id="fm" name="fm" method="post" action=""> 
        <input type="hidden" name="ac" value="{$ac}" /> 
        <ul class="mxui-user-list mxui-part-rows mxui-back-whits"> 
         <li class="mxui-padding-x mxui-part-rows mxui-line-bottom"> 
          <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
           <span class="mxui-col-xs3 mxui-col-sm3 mxui-part-eone">{if$ac=='phone'}手机{else}邮箱{/if}</span> 
           <span class="mxui-col-xs5 mxui-col-sm6 mxui-part-eone"><input type="text" class="mxui-form-control" name="to" class="member-input" placeholder="{if$ac=='phone'}手机号码{else}邮箱地址{/if}" /></span> 
           <span class="mxui-col-xs4 mxui-col-sm3 mxui-part-eone mxui-text-right "><button class="mxui-user-bind" type="button" id="btn_bind_send" value="获取验证码" />获取验证码</button></span> 
          </div> </li> 
         <li class="mxui-padding-x mxui-part-rows mxui-line-bottom"> 
          <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
           <span class="mxui-col-xs3 mxui-col-sm3 mxui-part-eone">验证码</span> 
           <span class="mxui-col-xs3 mxui-col-sm6 mxui-part-eone"><input type="text"  name="code" class="member-input mxui-form-control" placeholder="验证码" /></span> 
          </div> </li> 
         <li class="mxui-padding-x mxui-part-rows"><input class="mxui-subm-binds  mxui-rims-info mxui-btns-info mxui-btns-green mxui-btns" type="button" id="btn_submit" value="确认绑定" /> </li> 
        </ul> {if$__PAGING__.page_total>1} 
        <div class="mxui-page-info mxui-text-center"> 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-xs-inline{if$__PAGING__['page_current']==1} mxui-btns-disad{/if}" href="{$__PAGING__.page_url|mac_url_page=1}">首页</a> 
         <a class="mxui-btns-info mxui-rims-info{if$__PAGING__['page_current']==1} mxui-btns-disad{/if}" href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_prev}">上一页</a> {if$__PAGING__.page_current>3} 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline" href="{$__PAGING__.page_url|mac_url_page=1}">1</a> 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline mxui-btns-disad" href="javascript:;">...</a> {/if} {maccms:foreach name="$__PAGING__.page_num" id="num"} 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline{if$__PAGING__['page_current']==$num} mxui-btns-green{/if}" href="{if$__PAGING__['page_current']==$num}javascript:;{else}{$__PAGING__.page_url|mac_url_page=$num}{/if}">{$num}</a> {/maccms:foreach} {if$__PAGING__.page_current<($__PAGING__.page_total-2)} 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline mxui-btns-disad" href="javascript:;">...</a> 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline" href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_total}">{$__PAGING__.page_total}</a> {/if} 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-xs-inline" href="javascript:;">{$__PAGING__.page_current}/{$__PAGING__.page_total}</a> 
         <a class="mxui-btns-info mxui-rims-info{if$__PAGING__['page_current']==$__PAGING__.page_total} mxui-btns-disad{/if}" href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_next}">下一页</a> 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-xs-inline{if$__PAGING__['page_current']==$__PAGING__.page_total} mxui-btns-disad{/if}" href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_total}">尾页</a> 
        </div> {/if}{if$__PAGING__.record_total!=0} 
        <script type="text/javascript">
		if(document.getElementById('mxui-now')) document.getElementById('mxui-now').innerHTML='{$__PAGING__.page_current}';
		if(document.getElementById('mxui-count')) document.getElementById('mxui-count').innerHTML='{$__PAGING__.record_total}';
		</script> {/if} 
       </form> 
      </div> 
     </div> 
    </div> 
   </div> 
  </div> 
  {include file="public/foot"}
  <script type="text/javascript">
	var countdown=60;
	function settime(val) {
		if (countdown == 0) {
			val.removeAttribute("disabled");
			val.value="获取验证码";
			countdown = 60;
			return true;
		} else {
			val.setAttribute("disabled", true);
			val.value="重新发送(" + countdown + ")";
			countdown--;
		}
		setTimeout(function() {settime(val) },1000)
	}

	$('#btn_bind_send').click(function(){
	    var ac = $('input[name="ac"]').val();
		var to = $('input[name="to"]').val();
		if(ac=='email') {
            var pattern = /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
            var ex = pattern.test(to);
            if (!ex) {
                layer.msg('邮箱格式不正确');
                return;
            }
        }
        else if(ac=='phone') {
            var pattern=/^[1][0-9]{10}$/;
            var ex = pattern.test(to);
            if (!ex) {
                layer.msg('手机号格式不正确');
                return;
            }
        }
		else{
		    layer.msg('参数错误');
			return;
		}


		settime(this);
		var data = $("#fm").serialize();

		$.ajax({
			url: "{:url('user/bindmsg')}",
			type: "post",
			dataType: "json",
			data: data,
			beforeSend: function () {
				//开启loading
			},
			success: function (r) {
				layer.msg(r.msg);
			},
			complete: function () {
				//结束loading
			}
		});
	});

	$("#btn_submit").click(function() {
        var ac = $('input[name="ac"]').val();
        var to = $('input[name="to"]').val();

        if(ac=='email') {
            var pattern = /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
            var ex = pattern.test(to);
            if (!ex) {
                layer.msg('邮箱格式不正确');
                return;
            }
        }
        else if(ac=='phone') {
            var pattern=/^[1][0-9]{10}$/;
            var ex = pattern.test(to);
            if (!ex) {
                layer.msg('手机号格式不正确');
                return;
            }
        }
        else{
            layer.msg('参数错误');
            return;
        }

		var code = $('input[name="code"]').val();
		if(code==''){
			layer.msg('请输入验证码');
			return;
		}
		var data = $("#fm").serialize();

		$.ajax({
			url: "{:url('user/bind')}",
			type: "post",
			dataType: "json",
			data: data,
			beforeSend: function () {
				//开启loading
				//$(".loading_box").css("display","block");
				$("#btn_submit").val("loading...");
			},
			success: function (r) {
				layer.msg(r.msg);
				if(r.code==1){
					location.href="{:url('user/info')}";
				}
			},
			complete: function () {
				//结束loading
				//$(".loading_box").css("display","none");
				$("#btn_submit").val("提交");
			}
		});
	});
	
	$(".face").imageUpload({
		formAction: "{:url('user/portrait')}",
		inputFileName:'file',
		browseButtonValue: '',
		browseButtonClass:'btn btn-default btn-xs mxui-user-alter mxui-part-roun mxui-icon-font icon-xiugai',
		automaticUpload: true,
		hideDeleteButton: true,
		hover:true
	})
	$(".jQuery-image-upload-controls").mouseenter(function(){
    $(".jQuery-image-upload-controls").css("display","block");
	});
	$(".jQuery-image-upload-controls").mouseleave(function(){
    $(".jQuery-image-upload-controls").css("display","none");
	});
	$(".face").on("imageUpload.uploadFailed", function (ev, err) {
		layer.msg(err);
	});
</script>  
 </body>
</html>