<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no">
    <title>系统安全验证</title>
    <style>
    *,:after,:before{box-sizing:border-box}.mx-mac_msg_jump{margin:35px auto;padding:50px 25px;width:90%;border-radius:4px;box-shadow:0 .25rem .5rem rgba(0,0,0,.05),0 1.5rem 2.2rem rgba(0,0,0,.1);background:#fff;position:fixed;top:50%;left:50%;z-index:999999;transform:translateX(-50%) translateY(-50%);margin:0 auto;border-radius:5px}.mx-mac_msg_jump .msg_jump_tit{margin-bottom:25px;text-align:center;line-height:26px;color:#222;font-size:1.5rem;font-weight:700}.mx-mac_msg_jump .text{margin-bottom:10px;color:#222;font-size:14px}.mx-mac_msg_jump .title{margin-bottom:10px;color:#666;font-size:14px}@media (min-width:768px){.mx-mac_msg_jump{box-shadow:0 .25rem .5rem rgba(0,0,0,.05),0 1.5rem 2.2rem rgba(0,0,0,.1);margin:35px auto;padding:50px;width:400px}}.mx-mac_msg_jump .form .item1{position:relative;border:1px solid;border-radius:4px;line-height:43px;border-color:#f2f2f2}.mx-mac_msg_jump .form .item1 input{display:inline-block;padding:0 10px;width:65%;border:none;background:0 0;line-height:inherit}.item1 input:-webkit-autofill{-webkit-box-shadow:0 0 0px 1000px white inset}.mac_verify_img{float:right;width:35%;line-height:43px;vertical-align:middle;height:43px}.btnverify{display:inline-block;margin-top:25px;width:100%;border:none;border-radius:4px;line-height:45px;cursor:pointer;text-align:center;background-image:linear-gradient(-45deg,#00d3ff,#00b7ff);color:#fff}
    </style>
    <script>var maccms={"path":"__ROOT__","mid":"{$maccms['mid']}","url":"{$maccms['site_url']}","wapurl":"{$maccms['site_wapurl']}","mob_status":"{$maccms['mob_status']}"};</script>
   <script src="{$maccms.path}mxtheme/js/jquery.min.js"></script>
   <script src="{$maccms.path}static/js/home.js"></script>
   <script src="https://cdn.bootcdn.net/ajax/libs/layer/3.5.1/layer.min.js"></script>
    <script>
        $(function(){
            $('.mac_verify').focus();

            $("input[name='verify']").bind('keypress',function(event){
                if(event.keyCode == "13") {
                    if($("input[name='verify']").val()!=''){
                        $('.verify_submit').click();
                    }
                }
            });

            $('.verify_submit').click(function(){
                var v = $('input[name="verify"]').val();
                MAC.Ajax(maccms.path+'/index.php/ajax/verify_check?type={$type}&verify='+v,'post','json','',function(r){
                    if(r.code==1){
                        location.reload();
                    }
                    else{
                        layer.msg(r.msg);
                        MAC.Verify.Refresh();
                    }
                },function(){
                    layer.msg('请求失败，请重试');
                    MAC.Verify.Refresh();
                });
            });
        });
    </script>
</head>
<body>
  <div class="mx-mac_msg_jump">
    <div class="msg_jump_tit">系统提示</div>
    <div class="title">亲爱的用户:</div>
    <div class="text">访问此数据需要输入验证码</div>
    <div class="form">
            <div class="item1">
                 <input type="text" name="verify" class="mac_verify">
            </div>
            
            <div class="jump item">
        <input type="button" class="verify_submit submit_btn btnverify" value="提交验证">
    </div>
</div>
</div>  
</body>
</html>