                    	 {if condition="$mxprost['mxprocms']['s2']['zongkg'] eq 1"}
                    	<div class="fixedGroup">
                    	      {if condition="$mxprost['mxprocms']['s2']['themekg'] eq 1"}
                    	    <div class="fixedGroup-item">
                    	        <div class="fixedGroup__cell">
                    	            <span class="fixedGroup__triangle"></span>
                    	            <div class="txt" id="changeAppearance">切换深色外观</div>
                    	        </div>
                    	        <i  class="icon-yejian" data-id="black"></i>
                    	        <i class="icon-rijian" data-id="white"></i>
                    	    </div>
                    	    	{/if}
                    	    {if condition="$mxprost['mxprocms']['s2']['gbook'] eq 1"}
                    	    <div class="fixedGroup-item" onclick="location='{:mac_url('gbook/index')}'">
                    	        <div class="fixedGroup__cell">
                    	            <span class="fixedGroup__triangle"></span>
                    	            <div>留言</div>
                    	        </div>
                    	        <i class="icon-gbook"></i>
                    	    </div>
                    	   {/if}
                    	     {if condition="$mxprost['mxprocms']['s2']['up'] eq 1"}
                    	    <div class="fixedGroup-item retop">
                    	        <div class="fixedGroup__cell">
                    	            <span class="fixedGroup__triangle"></span>
                    	            <div>回到顶部</div>
                    	        </div>
                    	        <i class="icon-top"></i>
                    	    </div>
                    	      {/if}
                    	</div>
                    	{/if}
                    	<div class="footer">
						<div class="content">
					       {if condition="$mxprost['mxprocms']['s3']['indexlink'] eq 1"} {if condition="$maccms.aid eq 1"}{include file="index/link"}{/if}{/if}
							<div class="footer-content">
								<div class="foot-logo"><img class="logo2"  src="{:mac_url_img($mxprost.mxprocms.s1.logo2)}" alt="{$maccms.site_name}" >
								<img class="logo1" src="{:mac_url_img($mxprost.mxprocms.s1.logo1)}" /></div>
								<div class="foot-copyright">
								{if condition="$mxprost['mxprocms']['s2']['dbmap'] eq 1"}   
		                        <a target="_blank" href="{:mac_url('rss/index')}">RSS</a>
	                        	<a target="_blank" href="{:mac_url('rss/baidu')}">Baidu</a>
		                        <a target="_blank" href="{:mac_url('rss/baidu')}">Google</a>
	                        	<a target="_blank" href="{:mac_url('rss/sogou')}">Sogou</a>
	                        	{/if}
	                        	{if condition="$mxprost['mxprocms']['s2']['dbfk'] eq 1"}   
								<a href="{$mxprost.mxprocms.s2.dbfkurl}" target="_blank" rel="”nofollow”">{$mxprost.mxprocms.s2.dbfktext}</a>
								{/if}
								{if condition="$mxprost['mxprocms']['s2']['qqqun'] eq 1"} 
								<a href="{$mxprost.mxprocms.s2.qqqunurl}" target="_blank" rel="”nofollow”">{$mxprost.mxprocms.s2.qqquntext}</a>
								{/if}
								{if condition="$mxprost['mxprocms']['s2']['telegram'] eq 1"} 
								<a href="{$mxprost.mxprocms.s2.telegramurl}" target="_blank" rel="”nofollow”">{$mxprost.mxprocms.s2.telegramtext}</a>
								{/if}
								<p>{$maccms.site_name}{$mxprost.mxprocms.s1.sm}	<span class="none">{$mxprost.mxprocms.s5.dbdmtips}</span></p>
								</div>
							</div>
						</div>
					</div>
					
<script type="text/javascript">   
	document.onkeydown=function(){
	    
		var e = window.event||arguments[0];
		
	     {if condition="$mxprost['mxprocms']['s5']['shier'] eq 1"} 
			if(e.keyCode==123){
				alert('{$mxprost.mxprocms.s5.pbtips}');
				return false;
			}
        {/if}

        {if condition="$mxprost['mxprocms']['s5']['ctrl'] eq 1"} 
		if((e.ctrlKey)&&(e.shiftKey)&&(e.keyCode==73)){
			alert('{$mxprost.mxprocms.s5.pbtips}');
			return false;
		}
		if((e.ctrlKey)&&(e.keyCode==85)){
			alert('{$mxprost.mxprocms.s5.pbtips}');
			return false;
		}
		if((e.ctrlKey)&&(e.keyCode==83)){
		   alert('{$mxprost.mxprocms.s5.pbtips}');
		   return false;
		}
        {/if}
	}
  {if condition="$mxprost['mxprocms']['s5']['right'] eq 1"} 
	document.oncontextmenu=function(){
		alert('{$mxprost.mxprocms.s5.pbtips}');
		return false;
	}
 {/if}
  {if condition="$mxprost['mxprocms']['s5']['mode'] eq 1"} 
	var threshold = 160;
	window.setInterval(function() {  
	    if (window.outerWidth - window.innerWidth > threshold ||   
	    window.outerHeight - window.innerHeight > threshold) {  
			function disableDebugger() {
				debugger;
			}
			$(document).ready(function () {
				disableDebugger();
			});
	    }  
	}, 1e3);
 {/if}
</script>
					{if condition="$mxprost['mxprocms']['s5']['notice'] eq 1"}{include file="public/notice"}{/if}
