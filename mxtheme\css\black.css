body {
	background: #1A1C1F;
	color: #e1e3e5;
}

.logo1,
.icon-yejian {
	display: none!important
}

.icon-rijian {
	display: block!important
}

.header {
	background: #1A1C1F;
}

.header-op-list-btn {
	color: #e1e3e5;
}

.searchbar {
	background: #ffffff14;
}

.radian::before,
.navbar-item.active::before {
	background: #16161a;
}

.radian::after,
.navbar-item.active::after {
	background: #1A1C1F;
}

.radian .links::before,
.navbar-item.active .links::before,
.radian .links::before,
.navbar-item.active .links::before {
	background: #16161a;
}

.radian .links::after,
.navbar-item.active .links::after,
.radian .links::after,
.navbar-item.active .links::after {
	background: #1A1C1F;
}

.navbar-item.active a {
	color: #ff3300;
}

.navbar-item a:hover {
	color: #ff3300;
}

a {
	color: #e1e3e5;
}

@media (min-width: 560px) {
	.navbar-item:hover i.icon-arrow-go {
		color: #ff3300
	}
	.sidebar {
		background: #16161a;
		border-right: 1px solid #25252b;
	}
	.navbar-hr {
		background: #25252b;
	}
	.navbar-item.active::before,
	.navbar-item.active .links::before {
		border: 1px solid #25252b;
	}
	.navbar-item.active {
		background: linear-gradient(to right, #16161a 0%, #1A1C1F 90%, #1A1C1F 90%);
	}
	.navbar-item.active a {
		background: linear-gradient(to right, #16161a 0%, #1A1C1F 90%);
	}
	.navbar-item.active::before {
		border-width: 0 1px 1px 0;
	}
	.navbar-item.active .links::before {
		border-width: 1px 1px 0 0;
	}
	.module-tab-items {
		background: #16161a;
	}
	.module-tab-item.active {
		background: #25252b;
		color: #ff3d5e;
	}
	.module-info-content {
		background: #16161a;
	}
	.view .module-info-heading::after {
		border-left: 2px dashed #666;
	}
	.module-info-footer {
		background: #16161c;
	}
	.searchbar-main.open .search-recommend {
		background: #25252b;
		box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
	}
}

.module-tab-item {
	color: #e1e3e5;
}

.border::after,
.btn-block-o::after,
.play-btn-o::after,
.module-heading-more::after,
.module-tab-name::after,
.module-class::after,
.module-item-box a::after,
.module-info-tag-link::after,
.module-play-list-link::after {
	border: 1px solid #25252b;
}

.module-paper-item {
	background: #16161a;
}

.module-paper-item-main a::after {
	background: #1A1C1F;
}

.module-paper-item-main a:hover {
	background: #1A1C1F
}

.module-paper-item-main a::after {
	background: #1A1C1F
}

.links-list .module-heading {
	background: none;
}

.links-list {
	border-top: 2px solid #25252b;
}

.footer-content {
	background: #16161a;
}

.fixedGroup {
	background-color: #2b2f33;
	box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
}

.module-paper-item-title::after {
	background: #e1e3e5;
}

.module-paper-item-header {
	background: none;
}

.fixedGroup .fixedGroup-item:hover i {
	background-color: #3C3F43;
}

.module-info-tag-link {
	background: #32323c;
}

.module-info-tag-link span,
.module-info-tag-link a {
	color: #e1e3e5;
}

.border-bottom::after,
.module-heading-tab::after,
.module-tab-title::after,
.module-class-items::after,
.module-class-item::after,
.module-info-introduction::after {
	background-color: #25252b;
}

.border-top::after,
.drop-item-link::after,
.module-paper-item-main a::after,
.module-info-footer::after,
.popup-main p:last-child::after {
	background-color: #25252b;
}

.module-list {
	background: #16161a;
}

.module-play-list-link {
	color: #e1e3e5;
	background: #1a1c1f;
}

.module-play-list-link.active {
	color: #ff2a14;
	background: #25252b !important
}

.playon i {
	background-color: #ff2a14;
}

.module-play-list-link:hover {
	background: #25252b !important;
	color: #ff3d5e
}

.module-tab-item small {
	background: linear-gradient(90deg, #25252b, #32323c);
	color: #e1e3e5;
}

@media (min-width: 1025px) {
	.player-box {
		background: #16161a;
	}
	.player .player-heading {
		background-color: #25252b;
	}
	.module-player-info {
		background: #25252b;
	}
	.player .module-list.active {
		border-top: 1px solid #32323c;
	}
	.module-player-info .module-info-tag-link {
		background: #32323c;
	}
	.module-player-info .module-info-tag-link::after {
		border-color: #1a1c1f;
	}
	.player .player-heading .module-tab-item.active {
		background: #16161a;
		border: 1px solid #1a1c1f;
		border-bottom-color: #16161a;
	}
	.module-player-handle-items {
		border-top: 1px solid #25252b;
		background: #16161a;
	}
	.module-player-handle-item {
		border-left: 3px solid #25252b;
	}
}

.popup {
	box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
	background: #16161a;
}

.popup-title::before {
	background: none;
}

.report-content {
	background: #25252b;
}

.popup::after {
	background: #16161a;
}

.close-popup {
	box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
	background: #25252b;
}

.close-popup i {
	color: #e1e3e5;
}

.drop:hover .drop-content {
	background: #25252b;
}

.drop-qrcode-info-tips {
	background: #32323c;
}

.module-heading-tab .module-heading-tab-link.active {
	color: #e1e3e5;
}

.module-heading-tab a:hover.module-heading-tab-link,
.module-heading-tab-link:hover {
	color: #e1e3e5;
}

.module-heading-tab .module-heading-tab-link.active {
	color: #e1e3e5;
}

.line {
	background: #25252b;
}

.page-link {
	background: #25252b;
	color: #e1e3e5;
}

.page-current:hover,
.page-current {
	background: #ff3d5e!important;
	color: #e1e3e5!important;
}

.searchbar-main.open .searchbar,
.searchbar:hover {
	background: #25252b;
}

.search-tag a {
	background: #32323c;
	color: #e1e3e5;
}

.module-heading-search {
	border-bottom: 2px solid #25252b;
}

.module-card-item {
	background: #16161a;
}

.module-card-item-class {
	background: #25252b;
	color: #e1e3e5;
}

.module-card-item-class::after {
	background: #25252b;
}

.module-card-item-footer {
	background: #1a1c1f;
}

.module-card-item-footer a::before {
	border: 5px solid #16161a;
}

.play-btn-o {
	background: #1a1c1f;
}

.message .msg-content textarea {
	background-color: #16161a;
	border: 1px solid #25252b;
}

.form-control.verify {
	background-color: #16161a;
	border: 1px solid #25252b;
}

.message .msg-content .msg-wrap .msg-item .msg-item-left .uname {
	color: #e1e3e5;
}

.message .msg-content .msg-wrap .msg-item .msg-item-left .content {
	color: hsla(0, 0%, 100%, .87);
}

.msg-item,
.msg-item-left,
.info {
	color: #999;
}

.message .msg-content .msg-wrap .msg-item {
	border-bottom: 1px solid #25252b;
}

.drop-item::after {
	border-left: 1px dashed #32323c;
}

.drop-item-title i {
	background: #25252b;
}

.drop-item-link::before {
	background: #32323c;
}

.btn-gray,
.drop-item-op a {
	background: #32323c
}

.btn-gray:hover,
.drop-item-op a:hover {
	background: #2b2f33;
}

.btn-gray-dark {
	background: #25252b;
}

@media (max-width: 559px) {
    .module-poster-bg .module-item-cover:before {
		background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, #000 100%)
	}
	.searchbar {
		box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
	}
	.navbar-item a {
		color: #e1e3e5;
	}
	.navbar-item .current {
		background: none;
	}
	.navbar-item.active a {
		color: hsla(0, 0%, 100%, .87);
	}
	.navbar-item.active span::after {
		content: '';
		width: 35%;
		height: 4px;
		background: #ff2a14;
		background: linear-gradient(90deg, #ff9800, #ff2a14);
		display: inline-block;
		position: absolute;
		bottom: 3px;
		left: 32.5%;
		border-radius: 5px;
	}
	.searchbar-main.open {
		background: linear-gradient(to bottom, #1a1c1f 0%, #16161a 100%);
	}
	.searchbar-main.open::after {
		background: none;
	}
	.module-poster-items-small .module-poster-item {
		background: #25252b;
	}
	.module-tab-name {
		background: #25252b;
	}
	.module-tab .module-tab-items {
		background: #25252b;
	}
	.module-tab-item {
		background: #32323c;
	}
	.module-play-list-link.active .module-tab-item.active {
		color: hsla(0, 0%, 100%, .87);
		background: #ff3d5e;
	}
	.close-drop:hover {
		background: #32323c;
		color: #ff3d5e;
	}
	.sidebar-bg {
		background: #1a1c1f;
	}
	.module-info .module-main {
		box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
		background: #25252b;
	}
	.module-poster-bg::before {
		background: linear-gradient(to bottom, rgba(37, 37, 43, 0) 0%, #25252b 80%, #25252b 100%);
	}
	.links-list-go {
		color: hsla(0, 0%, 100%, .87);
		background: #25252b;
	}
	.module-player-info::after {
		box-shadow: 0 1px 0 #32323c;
	}
	.module-tab-item.active {
		color: hsla(0, 0%, 100%, .87);
		background: #ff3d5e;
	}
	.list .sidebar {
		background: #1a1c1f;
	}
	.module-heading-tab {
		background: #1a1c1f;
	}
	.line {
		background: #2b2f33;
	}
	.module-class {
		background: #25252b;
	}
	.module-item-box a {
		color: #e1e3e5;
	}
}

.foot-copyright p {
	color: hsla(0, 0%, 100%, .87);
}

@media (max-width: 1024px) {
	.module-player-info {
		background: #1a1c1f;
	}
	.handle-btn:hover {
		background: #25252b;
	}
}

.play-btn-o {
	color: #ff3d5e
}

#shortcuts-info {
	border: 2px solid #25252b;
}

.module-play-list::-webkit-scrollbar,
.v3-app-layout__side__Short::-webkit-scrollbar {
	width: 5px;
}

.module-play-list::-webkit-scrollbar-thumb,
.v3-app-layout__side__Short::-webkit-scrollbar-thumb {
	border-radius: 5px;
	height: 10px;
	background: #666;
}

.module-play-list::-webkit-scrollbar-track,
.v3-app-layout__side__Short::-webkit-scrollbar-track {
	background: transparent;
}

.player .module-list::-webkit-scrollbar,
.v3-app-layout__side__Short::-webkit-scrollbar {
	width: 5px;
}

.player .module-list::-webkit-scrollbar-thumb,
.v3-app-layout__side__Short::-webkit-scrollbar-thumb {
	border-radius: 5px;
	height: 10px;
	background: #666;
}

.player .module-list::-webkit-scrollbar-track,
.v3-app-layout__side__Short::-webkit-scrollbar-track {
	background: #25252b;
}

.module-title-en,
.module-ranking-tab-info {
	opacity: 0.3;
}

.module-paper-item-header>i {
	opacity: 0.2;
}
.app-pic::before, .app-pic::after{
    background: rgba(0, 0, 0, 0.28);
}
.module-tab-week{
		background: #16161a;
}

.mac_login_form .form-control{
    	border: 1px solid #32323c;
	background: #25252b;
}

@media (max-width: 559px){
    .m-module-tab-week .module-tab-item.active{color:#ff3d5e!important;}
}
@media (min-width: 560px){
	.module-info-content::after {
		box-shadow: 0 2.75px 2.21px rgb(225 227 229 / 1%), 0 6.65px 5.32px rgb(225 227 229 / 2%);
	}
}
.shadow,
.module-ranking-tab-link,
.module-paper-item,
.module-list {
	box-shadow: 0 2.75px 2.21px rgb(225 227 229 / 1%), 0 6.65px 5.32px rgb(225 227 229 / 2%);
}
@media (min-width: 1025px){
	.player-box::after {
		box-shadow: 0 2.75px 2.21px rgb(225 227 229 / 1%), 0 6.65px 5.32px rgb(225 227 229 / 2%);
	}
	.drop-qrcode-info-text p strong {
		color: #e1e3e5;
	}
}
@media (max-width: 1024px){
    .handle-btn {background: #25252b;}
}
/*会员中心*/
.mxui-part-layout,.mxui-user-head .mxui-hide-md{
    background: #16161a!important;
    border: 1px solid #25252b!important;
}
.mxui-line-bottom:after{
    background-color: #32323c!important;
}
.mxui-back-whits{
    color: #e1e3e5!important;
}
.mxui-line-left:before {border-left: .0625rem solid #32323c!important;}
.mxui-form-control{
    	background: #25252b!important;
}
.mxui-vodlist__text .striped-head, .mxui-vodlist__text.to-color li:nth-of-type(odd){background-color:#25252b!important;}
.mxui-line-top:before { top: 0; border-top: .0625rem solid #32323c!important }
.drop_content{
    	background: #25252b;
		box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
}
.member_group .user_list_drop .logout a:hover ,
.member_group .user_list_drop li a:hover{color:#ff3d5e}
.mxui-part-core-bg{
    	background:#16161a!important;
		box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%)!important;
}
.mxui-user-text,.mxui-user-yzm{
	border: 1px solid #32323c!important;
	background-color:#25252b!important;
}
.mxui-form-control:focus,.mxui-user-text:focus,.mac_login_form .form-control:focus {
	border: 1px solid #666!important;
}
.mxui-user-yzm{
    color: #e1e3e5!important;
}
.mx-mac_msg_jump{
    background: #16161a;
    box-shadow: 0 0 0 0;
}
.mx-mac_msg_jump .msg_jump_tit{
        color: hsla(0, 0%, 100%, .87);}
.mx-mac_msg_jump .text{
          color: hsla(0, 0%, 100%, .87); 
}     
.item1 input:-webkit-autofill {
	-webkit-box-shadow:0 0 0px 1000px #212124  inset
}
.mx-mac_msg_jump .form .item1{
    border-color: #212124;
}
.module-class::after{
    border-left: 0;
}
.module-prompt-info-bg::after{
    background:none;
}