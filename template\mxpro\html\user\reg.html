<!DOCTYPE html>
<html>
 <head> 
	{include file="public/include"}
  <title>会员注册 - 个人中心 - {$maccms.site_name}</title> 
  <meta name="keywords" content="个人中心,{$maccms.site_keywords}" /> 
  <meta name="description" content="{$maccms.site_description}" />
  {include file="user/include"}
 </head>
 <body class="mxui-min-width">
<div class="page list">
      {include file="public/head"} 
   <div class="mxui-part-case mxui-part-margin main"> 
    <div class="mxui-back-whits mxui-part-height"> 
     <div class="mxui-user-login mxui-part-core-bg"> 
      <div class="mxui-list-head mxui-part-rows mxui-padding"> 
       <h2 class="mxui-font-xvii mxui-text-center">会员注册</h2> 
      </div> 
      <form class="mxui-user-form mxui-user-width mxui-part-rows" method="post" id="fm" action=""> 
       <input class="mxui-user-text mxui-col-xs12" type="text" id="user_name" name="user_name" placeholder="请输入账号" /> 
       <input class="mxui-user-text mxui-col-xs12" type="password" id="user_pwd" name="user_pwd" placeholder="请输入密码" maxlength="20" /> 
       <input class="mxui-user-text mxui-col-xs12" type="password" id="user_pwd2" name="user_pwd2" placeholder="再次输入密码" maxlength="20" /> 
       {if condition="$user_config.reg_phone_sms neq 0"} 
	   <input type="hidden" name="ac" value="phone">
       <input class="mxui-user-text mxui-col-xs12" type="text" id="to" name="to" placeholder="请输入手机号" /> 
       <input class="mxui-user-text mxui-col-xs5" type="text" id="code" name="code" placeholder="请输入手机验证码" maxlength="20"/> 
       <input class="mxui-user-yzm mxui-col-xs4 mxui-yzm" type="button" id="btn_send_sms" value="获取验证码"/> 
       {elseif condition="$user_config.reg_email_sms neq 0"} 
	   <input type="hidden" name="ac" value="email">
       <input class="mxui-user-text mxui-col-xs12" type="text" id="to" name="to" placeholder="请输入邮箱" /> 
       <input class="mxui-user-text mxui-col-xs5" type="text" id="code" name="code" placeholder="请输入邮箱验证码" maxlength="20" /> 
       <input class="mxui-user-yzm mxui-col-xs4 mxui-yzm" type="button" id="btn_send_sms" value="获取验证码"/> 
       {/if} 
       {if condition="$user_config.reg_verify neq 0"} 
       <input class="mxui-user-text mxui-col-xs5" type="text" id="verify" name="verify" placeholder="请输入验证码" maxlength="20" /> 
       <img class="mxui-user-code mxui-user-text mxui-col-xs4" height="45" id="verify_img" src="{:mac_url('verify/index')}" onclick="this.src=this.src+'?'" title="看不清楚? 换一张！" /> {/if} 
       <input class="mxui-subm-regis mxui-user-submit mxui-rims-info mxui-btns-info mxui-btns-green mxui-col-xs12 m20" type="button" id="btn_submit" value="注册" /> 
       <a class="mxui-col-xs3 mxui-text-left" href="{:mac_url('user/login')}">立即登录</a>{if condition="$GLOBALS['config']['connect']['qq']['status'] eq 1"} 
       <a class="mxui-col-xs3 mxui-text-center" href="{:mac_url('user/oauth')}?type=qq">QQ登录</a> {/if} {if condition="$GLOBALS['config']['connect']['weixin']['status'] eq 1"} 
       <a class="mxui-col-xs3 mxui-text-center" href="{:mac_url('user/oauth')}?type=weixin">微信登录</a> {/if} 
       <a class="mxui-text-right" style="float:right" href="{:mac_url('user/findpass')}">忘记密码</a> 
      </form> 
     </div> 
    </div> 
   </div> 
  </div> 
  {include file="public/foot"}
  <script type="text/javascript">

    var countdown=60;
    function settime(val) {
        if (countdown == 0) {
            val.removeAttribute("disabled");
            val.value="获取验证码";
            countdown = 60;
            return true;
        } else {
            val.setAttribute("disabled", true);
            val.value="重新发送(" + countdown + ")";
            countdown--;
        }
        setTimeout(function() {settime(val) },1000)
    }


		$("body").bind('keyup',function(event) {
			if(event.keyCode==13){ $('#btnLogin').click(); }
		});

        $('#btn_send_sms').click(function(){
            var ac = $('input[name="ac"]').val();
            var to = $('input[name="to"]').val();
            if(ac=='email') {
                var pattern = /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
                var ex = pattern.test(to);
                if (!ex) {
                    layer.msg('邮箱格式不正确');
                    return;
                }
            }
            else if(ac=='phone') {
                var pattern=/^[1][0-9]{10}$/;
                var ex = pattern.test(to);
                if (!ex) {
                    layer.msg('手机号格式不正确');
                    return;
                }
            }
            else{
                layer.msg('参数错误');
                return;
            }


            settime(this);
            var data = $("#fm").serialize();

            $.ajax({
                url: "{:mac_url('user/reg_msg')}",
                type: "post",
                dataType: "json",
                data: data,
                beforeSend: function () {
                    //开启loading
                },
                success: function (r) {
                    layer.msg(r.msg);
                },
                complete: function () {
                    //结束loading
                }
            });
        });

		$('#btn_submit').click(function() {
			if ($('#user_name').val()  == '') { layer.msg('请输入用户！'); $("#user_name").focus(); return false; }
			if ($('#user_pwd').val()  == '') { layer.msg('请输入密码！'); $("#user_pwd").focus(); return false; }
			if ($('#verify').val()  == '') { layer.msg('请输入验证码！'); $("#verify").focus(); return false; }

			$.ajax({
				url: "{:mac_url('user/reg')}",
				type: "post",
				dataType: "json",
				data: $('#fm').serialize(),
				beforeSend: function () {
					$("#btn_submit").val("loading...");
				},
				success: function (r) {
					layer.msg(r.msg);
					if(r.code==1){
						location.href="{:mac_url('user/login')}";
					}
					else{
						$('#verify_img').click();
					}
				},
				complete: function () {
					$("#btn_submit").val("立即注册");
				}
			});

		});


</script>  
 </body>
</html>