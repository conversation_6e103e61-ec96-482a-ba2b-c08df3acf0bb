<style>
    input::-webkit-input-placeholder,
    textarea::-webkit-input-placeholder {
        color: #ccc;
    }
    
    input::-moz-placeholder,
    textarea::-moz-placeholder {
        color: #ccc;
    }
    
    input:-moz-placeholder,
    textarea:-moz-placeholder {
        color: #ccc;
    }
    
    input:-ms-input-placeholder,
    textarea:-ms-input-placeholder {
        color: #ccc;
    }
    
    .layui-tab {
        padding: 10px 30px 0 30px;
    }
    
    .layui-tab-content {
        padding: 15px 0 0 0;
    }
    
    .layui-form-select dl dd {
        background-color: #fff;
    }
    
    .layui-card {
        box-shadow: none;
        border: 1px solid #e6e6e6;
    }
    
    .layui-card-header {
        border-bottom: 1px solid #e6e6e6;
        background-color: #f2f2f2;
    }
    
    .layui-colla-content {
        color: inherit;
        padding: 15px;
    }
    
    .layui-elem-field {
        margin-left: 0;
        margin-right: 0;
        border-radius: 2px;
        margin-bottom: 15px;
    }
    
    .layui-elem-field legend {
        margin-left: 10px;
        font-size: 14px;
        font-weight: normal;
    }
    
    .layui-form-item .layui-input-company {
        width: auto;
        padding-right: 10px;
        line-height: 38px;
    }
    
    .layui-form-item {
        margin: 15px 0;
    }
    
    .layui-form-label {
        width: 120px;
    }
    
    .layui-input-block {
        margin-left: 150px;
    }
    
    .layui-form-onswitch em {
        margin-left: 0px;
    }
    
    .layui-layout-admin .layui-footer {
        background-color: #fff;
        padding: 10px 0;
        text-align: center;
        box-shadow: 0 -1px 2px 0 rgba(0, 0, 0, .05);
    }
</style>