* {
	margin: 0;
	padding: 0
}

*,
:after,
:before {
	box-sizing: border-box
}

body,
html {
	height: 100%
}

html {
	-webkit-tap-highlight-color: transparent;
	-ms-text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%;
	line-height: 1.6;
	font-size: 14px;
	-webkit-tap-highlight-color: transparent
}

input,
button,
textarea,
select {
	outline: none;
	resize: none;
	border: none;
	-webkit-appearance: none;
	appearance: none;
	background: transparent;
	color: inherit;
	font: inherit
}

body,
input,
textarea,
select,
button {
	-webkit-touch-callout: none;
	-webkit-font-smoothing: antialiased;
	font-family: -apple-system-font, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif
}

body {
	color: #282828;
	background: #f7f8f9
}

img {
	border: none
}

em {
	font-style: normal
}

button {
	cursor: pointer;
	background: none
}

button,
video {
	border: none;
	outline: none;
}

ol,
ul,
li,
dl,
dd,
dt {
	list-style: none
}

a {
	text-decoration: none;
	color: #282828;
	outline: none
}

a:hover {
	text-decoration: none;
	color: #e50914
}

table {
	empty-cells: show;
	border-collapse: collapse
}

caption,
th {
	text-align: left;
	font-weight: 400
}

select::-ms-expand,
input[type=radio]::-ms-check,
input[type=checkbox]::-ms-check,
input[type=text]::-ms-clear,
input[type=tel]::-ms-clear,
input[type=number]::-ms-clear {
	display: none
}

input[type="radio"],
input[type=checkbox] {
	clip: rect(0, 0, 0, 0)
}

.color-main,
.module-ranking-tab-link i,
.search-tag i,
.navbar-item.active a i {
	background: -webkit-linear-gradient(0deg, #ff711f, #e50914);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent
}

.navbar .swiper-slide {
	width: auto;
}

.navbar .swiper-slide,
#playSwiper .swiper-slide {
	width: auto;
}

.wrapper {
	width: 100%
}

.content {
	width: 100%;
	margin: 0 auto
}

.shadow-small,
.module-tab-item.active {
	box-shadow: 0px 0 5px 0 rgba(0, 0, 0, 0.08), 0 0 1px rgba(0, 0, 0, 0.1)
}

.shadow-base,
.searchbar-main.open .searchbar,
.searchbar:hover {
	box-shadow: 0px 0 10px 0 rgba(0, 0, 0, 0.1), 0 0 1px rgba(0, 0, 0, 0.1)
}

.shadow,
.module-ranking-tab-link,
.module-paper-item,
.module-list {
	box-shadow: 0 2.75px 2.21px rgba(0, 0, 0, 0.01), 0 6.65px 5.32px rgba(0, 0, 0, 0.02), 0 12.5px 10px rgba(0, 0, 0, 0.01), 0 22px 18px rgba(0, 0, 0, 0.03), 0 42px 33.4px rgba(0, 0, 0, 0.02), 0 100px 80px rgba(0, 0, 0, 0.01), 0 0 1px rgba(0, 0, 0, 0.1)
}

.shadow-drop,
.drop:hover .drop-content {
	box-shadow: 0px 10px 70px rgba(0, 0, 0, 0.15), 0 0 1px rgba(0, 0, 0, 0.18)
}

.transition {
	transition: all 0.3s ease-in
}

.transition-fast {
	transition: all 0.15s ease-in
}

.transition-bg-fast {
	transition: background .15s ease
}

.transition-bg,
.shortcuts-mobile-overlay {
	transition: background .3s ease
}

.impact,
.module-title-en,
.module-ranking-tab-info,
.module-item-top {
	font-family: "Impact", "system-ui", "Helvetica Neue", sans-serif;
	font-weight: 900
}

.radian,
.navbar-item.active,
.links {
	position: relative
}

.radian::after,
.navbar-item.active::after,
.radian::before,
.navbar-item.active::before {
	top: -19px;
	right: 0
}

.radian::before,
.navbar-item.active::before,
.radian::after,
.navbar-item.active::after,
.radian .links::before,
.navbar-item.active .links::before,
.radian .links::after,
.navbar-item.active .links::after {
	content: '';
	position: absolute;
	height: 20px;
	width: 20px
}

.radian::before,
.navbar-item.active::before {
	background: #fff;
	border-radius: 0 0 20px 0;
	z-index: 1
}

.radian::after,
.navbar-item.active::after {
	background: #f7f8f9
}

.radian .links::before,
.navbar-item.active .links::before,
.radian .links::after,
.navbar-item.active .links::after {
	bottom: -20px;
	right: -16px
}

.radian .links::before,
.navbar-item.active .links::before,
.radian .links::before,
.navbar-item.active .links::before {
	background: #fff;
	border-radius: 0 20px 0 0;
	z-index: 1
}

.radian .links::after,
.navbar-item.active .links::after,
.radian .links::after,
.navbar-item.active .links::after {
	background: #f7f8f9
}

.border,
.btn-block-o,
.play-btn-o,
.module-heading-more,
.module-tab-name,
.module-class,
.module-item-box a,
.module-info-tag-link,
.module-play-list-link {
	position: relative
}

.border::after,
.btn-block-o::after,
.play-btn-o::after,
.module-heading-more::after,
.module-tab-name::after,
.module-class::after,
.module-item-box a::after,
.module-info-tag-link::after,
.module-play-list-link::after {
	border: 1px solid #e9ecef;
	border-radius: 8px;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	content: "";
	-webkit-transform-origin: 0 0;
	transform-origin: 0 0;
	pointer-events: none
}

@media only screen and (max-width: 559px) {

	.border::after,
	.btn-block-o::after,
	.play-btn-o::after,
	.module-heading-more::after,
	.module-tab-name::after,
	.module-class::after,
	.module-item-box a::after,
	.module-info-tag-link::after,
	.module-play-list-link::after {
		width: 200%;
		height: 200%;
		-webkit-transform: scale(0.5);
		transform: scale(0.5);
		border-radius: 16px
	}
}

.border-top,
.drop-item-link,
.module-paper-item-main a,
.module-info-footer,
.popup-main p:last-child {
	position: relative
}

.border-top::after,
.drop-item-link::after,
.module-paper-item-main a::after,
.module-info-footer::after,
.popup-main p:last-child::after {
	content: " ";
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 1px;
	background-color: #f1f3f5
}

.border-bottom,
.module-heading-tab,
.module-tab-title,
.module-class-items,
.module-class-item,
.module-info-introduction {
	position: relative
}

.border-bottom::after,
.module-heading-tab::after,
.module-tab-title::after,
.module-class-items::after,
.module-class-item::after,
.module-info-introduction::after {
	content: " ";
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 1px;
	background-color: #f1f3f5
}

@media only screen and (max-width: 559px) {

	.border-top::after,
	.drop-item-link::after,
	.module-paper-item-main a::after,
	.module-info-footer::after,
	.popup-main p:last-child::after,
	.border-bottom::after,
	.module-heading-tab::after,
	.module-tab-title::after,
	.module-class-items::after,
	.module-class-item::after,
	.module-info-introduction::after {
		-webkit-transform: scaleY(0.5);
		transform: scaleY(0.5)
	}
}

@font-face {
	font-family: 'iconfont';
	src: url("../fonts/iconfont.eot?fanger");
	src: url("../fonts/iconfont.eot?fanger#iefix") format("embedded-opentype"), url("../fonts/iconfont.ttf?fanger") format("truetype"), url("../fonts/iconfont.woff?fanger") format("woff"), url("../fonts/iconfont.svg?fanger#wpfont") format("svg");
	font-weight: normal;
	font-style: normal;
	font-display: block
}

[class^="icon-"],
[class*=" icon-"] {
	font-family: 'iconfont' !important;
	speak: never;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	vertical-align: revert;
	display: inline-block;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale
}

.icon-info:before {
	content: "\e91a"
}

.icon-next:before {
	content: "\e91b"
}

.icon-share:before {
	content: "\e91c"
}

.icon-qrcode:before {
	content: "\e91d"
}

.icon-arrow-o:before {
	content: "\e919"
}

.icon-close:before {
	content: "\e917"
}

.icon-arrow:before {
	content: "\e918"
}

.icon-history:before {
	content: "\e915"
}

.icon-hot:before {
	content: "\e916"
}

.icon-jl:before {
	content: "\e913"
}

.icon-jl-o:before {
	content: "\e914"
}

.icon-arrow-go:before {
	content: "\e911"
}

.icon-arrow-right:before {
	content: "\e912"
}

.icon-ranking:before {
	content: "\e90d"
}

.icon-ranking-o:before {
	content: "\e90e"
}

.icon-update:before {
	content: "\e90f"
}

.icon-update-o:before {
	content: "\e910"
}

.icon-search:before {
	content: "\e900"
}

.icon-play:before {
	content: "\e901"
}

.icon-history-o:before {
	content: "\e902"
}

.icon-zy:before {
	content: "\e903"
}

.icon-zy-o:before {
	content: "\e904"
}

.icon-dy:before {
	content: "\e905"
}

.icon-dy-o:before {
	content: "\e906"
}

.icon-dm:before {
	content: "\e907"
}

.icon-dm-o:before {
	content: "\e908"
}

.icon-home:before {
	content: "\e909"
}

.icon-home-o:before {
	content: "\e90a"
}

.icon-tv:before {
	content: "\e90b"
}

.icon-tv-o:before {
	content: "\e90c"
}

.icon-pets:before {
	content: "\e920"
}

.icon-car:before {
	content: "\e921"
}

.icon-zhuomian:before {
	content: "\e922"
}

.icon-game:before {
	content: "\e923"
}

.icon-love:before {
	content: "\e924"
}

.icon-fun:before {
	content: "\e925"
}

.icon-sp:before {
	content: "\e926"
}

.icon-junshi:before {
	content: "\e927"
}

.icon-keji:before {
	content: "\e928"
}

.icon-gbook:before {
	content: "\e929"
}

.icon-shop:before {
	content: "\e930"
}

.icon-qi:before {
	content: "\e931"
}

.icon-food:before {
	content: "\e932"
}

.icon-music:before {
	content: "\e933"
}

.icon-nba:before {
	content: "\e934"
}

.icon-nongren:before {
	content: "\e935"
}

.icon-rijian:before {
	content: "\e936"
}

.icon-shangchuan:before {
	content: "\e937"
}

.icon-child:before {
	content: "\e938"
}

.icon-score:before {
	content: "\e939"
}

.icon-zhifeiji:before {
	content: "\e940"
}

.icon-tianjia:before {
	content: "\e941"
}

.icon-top:before {
	content: "\e942"
}

.icon-tvs:before {
	content: "\e943"
}

.icon-sports:before {
	content: "\e944"
}

.icon-vlog:before {
	content: "\e945"
}

.icon-wenhua:before {
	content: "\e946"
}

.icon-wuzhanai:before {
	content: "\e947"
}

.icon-notice:before {
	content: "\e948"
}

.icon-recreation:before {
	content: "\e949"
}

.icon-movies:before {
	content: "\e950"
}

.icon-yejian:before {
	content: "\e951"
}

.icon-diy:before {
	content: "\e952"
}

.icon-shujia:before {
	content: "\e953"
}

.icon-download:before {
	content: "\e954"
}

.icon-down:before {
	content: "\e955"
}

.icon-link:before {
	content: "\e956"
}

.icon-report:before {
	content: "\e957"
}

.icon-fenxiang:before {
	content: "\e958"
}

.icon-apple:before {
	content: "\e959"
}

.icon-android:before {
	content: "\e960"
}

.icon-windows:before {
	content: "\e961"
}

.icon-apptuisong:before {
	content: "\e962"
}

.icon-app:before {
	content: "\e963"
}

.icon-shuaxin:before {
	content: "\e964"
}

.icon-right:before {
	content: "\e965"
}

.icon-left:before {
	content: "\e966"
}

.icon-full:before {
	content: "\e967"
}

.icon-sort:before {
	content: "\e968"
}

.icon-sorts:before {
	content: "\e972"
}

.icon-sort-o:before {
	content: "\e970"
}

.icon-bilibili:before {
	content: "\e969"
}

.icon-fullscreen:before {
	content: "\e971"
}

.icon-phone-o:before {
	content: "\e973"
}

.icon-week-o:before {
	content: "\e976"
}

.icon-xiugai:before {
	content: "\e977"
}

.icon-yonghu:before {
	content: "\e978"
}

.icon-you:before {
	content: "\e979"
}

.icon-yh:before {
	content: "\e980"
}

.icon-sz:before {
	content: "\e981"
}

.icon-vip:before {
	content: "\e982"
}

.icon-exit:before {
	content: "\e983"
}

.icon-warm:before {
	content: "\e984"
}

.icon-login:before {
	content: "\e985"
}

.icon-yonghu-o:before {
	content: "\e986"
}

.icon-shoucang:before {
	content: "\e987"
}

.icon {
	width: 16px;
	height: 16px;
	font-size: 16px;
	margin-right: 5px
}

.icon64 {
	width: 64px;
	height: 64px;
	font-size: 64px
}

.icon20 {
	width: 20px;
	height: 20px;
	font-size: 20px;
	color: #fff;
}

.active-bg,
.module-heading-tab .module-heading-tab-link.active {
	position: relative;
	z-index: 1
}

.active-bg::after,
.module-heading-tab .module-heading-tab-link.active::after {
	content: '';
	position: absolute;
	left: 0;
	bottom: -10%;
	width: 100%;
	background: #e50914;
	height: 40%;
	z-index: -1
}

.shortcuts-mobile-overlay {
	pointer-events: none;
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background-color: transparent;
	content: "";
	z-index: 19
}

.flex,
.logo,
.header-box,
.module-heading,
.module-paper-item-main a,
.searchbar,
.footer-content {
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	-webkit-box-direction: normal;
	-webkit-box-orient: horizontal;
	-webkit-flex-direction: row;
	-moz-flex-direction: row;
	-ms-flex-direction: row;
	flex-direction: row;
	-webkit-align-items: center;
	-moz-align-items: center;
	-ms-align-items: center;
	align-items: center
}

.flex-end,
.header-op,
.module-tab {
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	-webkit-box-direction: normal;
	-webkit-box-orient: horizontal;
	-webkit-flex-direction: row;
	-moz-flex-direction: row;
	-ms-flex-direction: row;
	flex-direction: row;
	-webkit-justify-content: flex-end;
	-moz-justify-content: flex-end;
	-ms-justify-content: flex-end;
	justify-content: flex-end;
	-ms-flex-pack: flex-end
}

.flex-start,
.search-box {
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	-webkit-box-direction: normal;
	-webkit-box-orient: horizontal;
	-webkit-flex-direction: row;
	-moz-flex-direction: row;
	-ms-flex-direction: row;
	flex-direction: row;
	-webkit-align-items: flex-start;
	-moz-align-items: flex-start;
	-ms-align-items: flex-start;
	align-items: flex-start
}

.nowrap,
.drop-item-link,
.module-item-note,
.module-poster-item-info .module-item-note,
.module-poster-item-title,
.module-paper-item-info,
.module-card-item-title,
.module-info-item-content,
.module-play-list-link {
	display: block;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap
}

.clearfix {
	*zoom: 1
}

.clearfix:after,
.clearfix:before {
	display: table;
	content: ""
}

.clearfix:after {
	clear: both
}

.disabled {
	cursor: not-allowed !important
}

.line {
	width: 3px;
	background: #e3e6ea;
	display: inline-block;
	height: 25px;
	margin: 0 15px
}

.slash {
	padding: 0 7px;
	color: #c2c6d0;
	font-size: 12px;
	vertical-align: top
}

.slash:last-child {
	display: none;
}

.playon {
	height: 8px;
	position: absolute;
	left: calc(50% - 11px);
	bottom: 0
}

.playon i {
	width: 4px;
	height: 6px;
	border-radius: 4px 4px 0 0;
	background-color: #e50914;
	position: absolute;
	bottom: 0;
	left: 0
}

.playon i:nth-last-child(1) {
	animation: playon .8s .3s infinite
}

.playon i:nth-last-child(2) {
	animation: playon .8s .1s infinite;
	left: 6px
}

.playon i:nth-last-child(3) {
	animation: playon .6s .2s infinite;
	left: 12px
}

.playon i:nth-last-child(4) {
	animation: playon 1s .3s infinite;
	left: 18px
}

@keyframes playon {
	0% {
		height: 70%
	}

	50% {
		height: 100%
	}

	100% {
		height: 35%
	}
}

.btn-xsmall {
	padding: 0 5px;
	line-height: 20px;
	font-size: 12px;
	border-radius: 4px;
	display: inline-block !important
}

.btn-small,
.drop-item-op a,
.module-card-item-footer a,
.module-tab-name,
.module-info-tag-link {
	padding: 0 10px;
	line-height: 30px;
	height: 30px;
	font-size: 13px;
	border-radius: 8px;
	display: inline-block
}

.btn-base,
.module-heading-more,
.module-tab-item,
.search-tag a,
.module-play-list-link,
.page-more {
	padding: 0 15px;
	line-height: 34px;
	font-size: 14px;
	display: inline-block;
	border-radius: 8px
}

.btn-large,
.main-btn,
.module-prompt p a,
.popup-btn,
.app-downlist .app-downlist-go,
.header-op-list-btn {
	padding: 0 15px;
	line-height: 40px;
	font-size: 14px;
	display: inline-block;
	border-radius: 8px
}

.btn-block-o {
	text-align: center
}

@media (min-width: 559px) {
	.navbar .swiper-wrapper {
		flex-direction: column;
	}

	.module-wrapper {
		display: flex;
	}
}

@media (max-width: 559px) {

	.btn-base,
	.module-heading-more,
	.module-tab-item,
	.search-tag a,
	.module-play-list-link,
	.page-more {
		padding: 0 10px;
		line-height: 30px;
		font-size: 12px
	}

	.app-pic {
		display: none !important
	}
}

.btn-gray,
.drop-item-op a {
	background: #f3f5f7
}

.btn-gray:hover,
.drop-item-op a:hover {
	background: #f1f3f5
}

.btn-gray-dark {
	background: #e9ecef
}

.btn-main {
	background: #e50914;
	color: #fff
}

.btn-main-linear,
.main-btn,
.module-prompt p a,
.popup-btn,
.app-downlist .app-downlist-go {
	transition: box-shadow .2s ease;
	background: #e50914;
	color: #fff;
	background: linear-gradient(to right, #ff711f 0%, #e50914 100%);
	box-shadow: 0 10px 12px -4px rgba(229, 9, 20, 0.25)
}

.btn-main-linear:hover,
.main-btn:hover,
.module-prompt p a:hover,
.popup-btn:hover,
.app-downlist .app-downlist-go:hover {
	box-shadow: 0 10px 12px -4px rgba(229, 9, 20, 0.4)
}

.btn-aux-linear {
	background: #fcd877;
	background: linear-gradient(90deg, #f9f2df, #fcd877) !important
}

.main-btn,
.module-prompt p a,
.popup-btn,
.app-downlist .app-downlist-go {
	position: relative;
	overflow: hidden;
	border-radius: 50px;
	font-weight: 700;
	padding: 0 30px
}

.main-btn:hover,
.module-prompt p a:hover,
.popup-btn:hover,
.app-downlist .app-downlist-go:hover {
	color: #fff
}

.main-btn::after,
.module-prompt p a::after,
.popup-btn::after,
.app-downlist .app-downlist-go::after {
	content: '';
	height: 50px;
	width: 25px;
	background: #fff;
	position: absolute;
	top: -5px;
	transform: skewX(-45deg);
	right: 115%;
	opacity: .25
}

.main-btn:hover::after,
.module-prompt p a:hover::after,
.popup-btn:hover::after,
.app-downlist .app-downlist-go:hover::after {
	right: -44%;
	transition: right .56s ease
}

.main-btn i,
.noplaylist i,
.btn-collect i,
.module-prompt p a i,
.popup-btn i,
.app-downlist .app-downlist-go i {
	margin-right: 6px;
	vertical-align: 0;
	height: 14px;
	width: 14px;
	font-size: 12px
}

.play-btn {
	background: #e50914;
	color: #fff;
	background: linear-gradient(to right, #ff711f 0%, #e50914 100%);
	color: #fff;
	padding: 0 15px
}

.play-btn:hover {
	color: #fff
}

.play-btn i {
	font-size: 12px;
	transform: scale(0.8)
}

.play-btn.icon-btn {
	width: 30px;
	padding: 0;
	text-indent: 5px
}

.play-btn-o {
	background: #fff;
	color: #e50914;
	padding: 0 15px
}

.play-btn-o::after {
	border-color: #e50914
}

.play-btn-o:hover {
	background: #e50914;
	color: #fff;
	background: linear-gradient(to right, #ff711f 0%, #e50914 100%);
	color: #fff
}

.play-btn-o:hover::after {
	border-color: transparent
}



@media (max-width: 1024px) {

	.main-btn::after,
	.module-prompt p a::after,
	.popup-btn::after,
	.app-downlist .app-downlist-go::after {
		right: -44%;
		opacity: 0;
		text-indent: 5px
	}
}

.drop-content {
	position: absolute;
	top: 40px;
	font-size: 0;
	right: 0;
	border-radius: 8px;
	min-width: 300px;
	max-width: 330px;
	pointer-events: none;
	height: auto;
	overflow: hidden;
	z-index: 10
}

.drop-content-box {
	opacity: 0
}

.drop-content-items {
	padding: 15px;
	position: relative
}

.drop-content-items li {
	font-size: 14px
}

.drop {
	position: relative;
	display: inline-block
}

.drop:hover::before {
	content: '';
	position: absolute;
	bottom: 0;
	width: 300%;
	right: 0;
	height: 5px
}

.drop:hover .drop-content {
	pointer-events: auto;
	z-index: 20;
	background: #fff
}

.drop:hover .drop-content-box {
	opacity: 1
}

.drop-item {
	position: relative
}

.drop-item::after {
	content: '';
	border-left: 1px dashed #dbdee2;
	position: absolute;
	left: 13px;
	top: 0;
	height: 100%;
	z-index: -1
}

.drop-item.nolist::after {
	border: 0
}

.drop-item-title {
	padding-bottom: 5px
}

.drop-item-title strong {
	font-size: 16px
}

.drop-item-title i {
	width: 35px;
	padding-top: 12px;
	background: #fff;
	text-indent: 5px;
	margin: 0;
	vertical-align: -1px;
	height: 100%;
	color: #e50914
}

.drop-item-link {
	border-radius: 0;
	position: relative;
	border-radius: 8px;
	line-height: 30px;
	padding: 5px 5px 2px 35px;
	width: 100%
}

.drop-item-link::before {
	content: '';
	height: 9px;
	width: 9px;
	display: inline-block;
	position: absolute;
	left: 9px;
	border: 1px solid #e50914;
	background: #fff;
	border-radius: 50%;
	top: 15px
}

.drop-item-link::after {
	width: 90% !important;
	left: 10% !important
}

.drop-item-link:hover::before {
	background: #e50914
}

.drop-item-link::after {
	background: #f7f8f9
}

.drop-history .drop-content-items {
	padding: 10px 15px 15px
}

.drop-item-link span {
	float: right;
	color: #a0a0a0;
	overflow: hidden;
	max-width: 24%;
	min-width: 18%;
	font-size: 12px;
	text-align: right
}

.drop-item-op {
	padding-top: 10px
}

.drop-item-op a {
	width: 100%;
	text-align: center;
	cursor: pointer;
	display: block;
	;
}

.drop-item-op a:hover {
	background: #ffddd5;
	color: #e50914
}

.drop-prompt {
	padding: 130px 35px 0;
	background: url(../images/history.svg) no-repeat center 20px;
	height: 165px;
	background-size: 40%;
	text-align: center;
	color: #9e9e9e;
	font-size: 13px
}

.nolist+.drop-item-op {
	display: none
}

.sidebar {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 10
}

.logo {
	width: 200px;
	position: absolute;
	left: -240px;
	padding-left: 30px
}

.logo a {
	height: 24px
}

.logo img {
	display: block;
	height: 100%
}

.header {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 10;
	width: calc(100% - 200px)
}

.header-box {
	position: relative;
	height: 64px;
	z-index: 9
}

.header-op {
	flex: 0 auto;
	margin-left: 10px;
	font-size: 0
}

.side-op {
	display: none
}

.header-op-search {
	display: none
}

.header-op-list-btn {
	cursor: pointer;
	color: #424242
}

.module {
	padding-bottom: 20px;
	font-size: 0
}

.module-main {
	position: relative
}

.module-heading {
	margin-bottom: 25px;
	font-size: 0;
	vertical-align: baseline;
	position: relative
}

.module-heading-tab {
	padding-bottom: 20px
}

.pianku .module-heading-tab {
	margin-bottom: -2px
}

.module-heading-tab::after {
	background: #e9ecef
}

.module-heading-tab-link,
.module-title {
	font-size: 30px;
	font-weight: 700;
	line-height: 1.1;
	position: relative;
	z-index: 2;
	flex: 1;
	-webkit-font-smoothing: subpixel-antialiased
}

.module-title span {
	cursor: pointer
}

.module-heading-tab-link {
	flex: initial;
	color: #a0a0a0;
	cursor: pointer
}

.module-heading-tab a:hover.module-heading-tab-link,
.module-heading-tab-link:hover {
	color: #212121
}

.module-heading-tab a.module-heading-tab-link {
	color: #a0a0a0
}

.module-heading-tab .module-heading-tab-link.active {
	color: #212121
}

.module-heading-tab .module-heading-tab-link.active:hover {
	cursor: text
}

.module-heading-tab .module-heading-tab-link.active::after {
	bottom: -60%;
	height: 4px;
	border-radius: 18px 18px 0 0
}

.module-heading-search {
	border-bottom: 2px solid #e9ecef;
	padding-bottom: 15px
}

.module-heading-search-result {
	font-size: 20px;
	font-weight: 700
}

.module-heading-search-result strong {
	color: #e50914
}

.module-title-en,
.module-ranking-tab-info {
	transition: color .3s ease;
	opacity: .12;
	background: -webkit-linear-gradient(90deg, transparent, #e50914);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	white-space: nowrap;
	position: absolute;
	left: 0;
	bottom: 0;
	font-size: 38px;
	z-index: -1
}

.module-title a {
	transition: color .3s ease
}

.module-heading-more {
	margin: -3px 0;
	cursor: pointer
}

.module-heading-more i {
	font-weight: 900;
	font-size: 10px;
	margin: 0 -5px 0 3px;
	transform: scale(0.8)
}

.module-tab {
	margin: -3px 0
}

.module-tab-name,
.module-tab-title,
.module-tab-input {
	display: none
}

.module-tab-item {
	color: #616161;
	text-align: center;
}

.speed-prompt {
	display: none
}

.speed-level {
	position: absolute;
	top: 2px;
	left: 0;
	display: inline-block;
	width: 9px;
	height: 9px
}

.speed-level i {
	position: absolute;
	left: 0;
	display: inline-block;
	width: 2px;
	height: 40%;
	background: #757575;
	opacity: .2;
	bottom: 0;
	border-radius: 3px
}

.speed-level i:nth-child(2) {
	left: 3px;
	height: 60%
}

.speed-level i:nth-child(3) {
	left: 6px;
	height: 80%
}

.speed-level i:nth-child(4) {
	left: 9px;
	height: 100%
}

.speed.slow .speed-level i:nth-child(-n+1),
.speed.fast .speed-level i:nth-child(-n+3),
.speed.faster .speed-level i:nth-child(-n+2),
.speed.fastest .speed-level i {
	opacity: 1
}

.speed {
	position: absolute;
	left: 7px;
	top: 3px;
	width: 100%;
	word-break: break-all;
	word-wrap: break-word;
	text-align: center
}

.speed:hover .speed-prompt {
	position: absolute;
	left: -2px;
	top: 13px;
	padding: 6px 5px;
	line-height: 1;
	font-size: 12px;
	background: #f3f5f7;
	color: #212121;
	font-weight: 400;
	width: calc(100% - 10px);
	border-radius: 5px
}

.speed.fastest .speed-prompt,
.speed.faster .speed-prompt,
.speed.fast .speed-prompt {
	background: #d5f7d5;
	color: green
}

.speed.slow .speed-prompt {
	background: #fff0ec;
	color: #e50914
}

.module-ranking-tab {
	margin-bottom: 25px
}

.module-ranking-tab-link {
	background: #fff;
	padding: 15px 20px 40px;
	display: inline-block;
	margin-right: 15px;
	border-radius: 8px;
	position: relative;
	z-index: 1
}

.module-ranking-tab-link i {
	font-size: 20px
}

.module-ranking-tab-name {
	font-size: 18px;
	width: 100%;
	min-width: 80px;
	font-weight: 700;
	margin-top: 5px;
	display: block
}

.module-ranking-tab-info {
	font-size: 38px;
	width: 100%;
	text-align: center;
	bottom: -15px
}

.module-items {
	margin-right: -18px
}

.module-item {
	position: relative;
	display: inline-block;
	font-size: 14px;
	margin: 0 18px 23px 0
}

.module-item-cover {
	transition: box-shadow .15s ease;
	position: relative;
	height: 0;
	padding-top: 140%;
	background-position: 50% 50%;
	background-size: cover;
	overflow: hidden;
	border-radius: 8px
}

.module-item-pic,
.module-item-pic img {
	height: 100%;
	width: 100%
}

.module-item-pic img {
	top: 0;
	left: 0;
	object-fit: cover;
	position: absolute
}

.module-item-nopic .module-item-pic::before {
	content: '';
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	top: 0;
	z-index: 1;
	border: 2px dashed #e3e6ea;
	border-radius: 10px
}

.module-item-nopic .module-item-note {
	display: none
}

.module-item-note {
	text-align: center;
	font-size: 12px
}

.module-item-cover .module-item-note {
	position: absolute;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1;
	background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.4) 100%);
	padding: 50px 5px 10px;
	pointer-events: none;
	color: #fff;
	font-weight: 500
}

.module-item-cover .module-item-note::after {
	content: '';
	width: 100%;
	height: 100%
}

.module-item-top {
	position: absolute;
	left: 0;
	top: 0;
	overflow: hidden;
	border-radius: 8px;
	z-index: 1;
	color: #fff;
	font-size: 30px;
	width: 40px;
	height: 50px;
	text-indent: 9px;
	text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.1);
	font-weight: 700
}

.module-item-top::after {
	content: '';
	position: absolute;
	left: -100%;
	top: -44%;
	z-index: -1;
	background: #9e9e9e;
	height: 150%;
	width: 150%;
	border-radius: 16px;
	transform: rotateZ(45deg)
}

.module-item-top.top1::after {
	background: #e50914
}

.module-item-top.top2::after {
	background: #f73
}

.module-item-top.top3::after {
	background: #ffa82e
}

.module-poster-item {
	width: calc(12.5% - 18px)
}

.module-poster-items-small .module-item-cover {
	padding-top: 58%
}

.module-poster-item-info {
	margin-top: 12px
}

.module-poster-item-info .module-item-note {
	margin-top: 3px;
	color: #757575
}

.module-poster-item-title {
	text-align: center;
	font-size: 14px
}

.module-poster-items-small .module-item-note {
	margin: 0;
	position: absolute;
	top: 5px;
	right: 5px;
	left: initial;
	bottom: initial;
	color: #fff;
	padding: 2px 8px;
	background: #e50914;
	border-radius: 8px 7px 8px 8px
}

.module-paper-item {
	width: calc(25% - 18px);
	background: #fff;
	position: relative;
	border-radius: 8px;
	overflow: hidden;
	z-index: 2
}

.module-paper-item-header {
	padding: 20px 15px 25px;
	text-align: center;
	background: url(../images/colour-bg.png) no-repeat;
	background-size: 150%;
	background-position-y: -20px
}

.module-paper-item-header>i {
	position: absolute;
	left: 38%;
	top: -15px;
	color: #e50914;
	opacity: .06
}

.module-paper-item-title {
	font-size: 18px;
	display: inline-block;
	position: relative
}

.module-paper-item-title::after {
	content: '';
	position: absolute;
	bottom: -12px;
	left: 40%;
	width: 20%;
	background: #212121;
	height: 5px;
	border-radius: 50px
}

.module-paper-item-main a {
	padding: 10px 0;
	margin: 0 10px;
	min-height: 45px
}

.module-paper-item-main a:first-child::after {
	background: transparent
}

.module-paper-item-main a:hover {
	margin: 0;
	padding: 10px;
	background: #f7f8f9
}

.module-paper-item-main a::after {
	background: #f7f8f9
}

.module-paper-item-num {
	font-family: "Impact", "Roboto-Black", "Helvetica Neue", sans-serif;
	font-weight: 700;
	position: relative;
	color: #bcbcbc;
	font-size: 24px;
	line-height: 1;
	width: 50px;
	text-align: center
}

.module-paper-item-num-first {
	color: #e50914
}

.module-paper-item-num-second {
	color: #f73
}

.module-paper-item-num-third {
	color: #ffa82e
}

.module-paper-item-info {
	max-width: 70%
}

.module-paper-item-info p {
	color: #9e9e9e;
	font-size: 12px
}

.module-paper-item-main .icon-arrow-right {
	position: absolute;
	right: 12px;
	font-size: 10px;
	color: #c2c6d0;
	margin-top: 2px
}

.module-paper-item-main a:hover .icon-arrow-right {
	right: 22px;
	color: #e50914
}

.module-card-items {
	padding-top: 30px;
	margin-bottom: -30px
}

.module-card-item {
	width: calc(25% - 18px);
	box-shadow: 0 2px 2px rgba(0, 0, 0, 0.03);
	background: #fff;
	padding: 20px 20px 10px;
	margin-bottom: 50px !important;
	position: relative;
	border-radius: 8px
}

.module-card-item.top .module-item-cover::before {
	content: '';
	height: 100%;
	width: 100%;
	position: absolute;
	left: 0;
	top: 0;
	border-radius: 7px;
	border: 3px solid #e50914;
	z-index: 2
}

.module-card-item.top2 .module-item-cover::before {
	border-color: #f73
}

.module-card-item.top3 .module-item-cover::before {
	border-color: #ffa82e
}

.module-card-item-class {
	position: absolute;
	right: 0;
	top: -25px;
	height: 40px;
	background: #e9ecef;
	color: #616161;
	font-size: 13px;
	padding: 0 10px 0 2px;
	border-radius: 8px;
	font-weight: 700;
	line-height: 26px;
	z-index: -1
}

.module-card-item-class::after {
	content: '';
	position: absolute;
	left: -13px;
	top: 0;
	background: #e9ecef;
	border-radius: 8px;
	width: 100%;
	height: 100%;
	transform: skewX(-20deg);
	z-index: -1
}

.module-card-item-class i {
	vertical-align: -1px;
	margin-right: 5px
}

.module-card-item-poster {
	width: 120px;
	margin: -45px 0 0 -10px;
	float: left;
	position: relative;
	z-index: 1
}

.module-card-item-info {
	margin-left: 130px;
	min-height: 103px
}

.module-card-item-title {
	font-size: 16px;
	margin-bottom: 8px;
	line-height: 1.2
}

.module-card-item-info .module-info-item {
	font-size: 13px;
	padding-bottom: 3px;
	color: #757575
}

.module-card-item-info .module-info-item-title {
	min-width: 37px;
	font-weight: 400
}

.module-card-item-info .module-info-item-link {
	margin-right: 8px
}

.module-card-item-footer {
	background: #f3f5f7;
	height: 30px;
	margin: 0 -10px;
	border-radius: 8px
}

.module-card-item-footer a {
	margin: 0 0 0 20px;
	z-index: 1;
	position: relative;
	top: -15px;
	float: left
}

.module-card-item-footer a::before {
	content: '';
	height: 100%;
	width: 100%;
	border-radius: 50px;
	position: absolute;
	left: -5px;
	border: 5px solid #fff;
	box-sizing: content-box;
	z-index: 1;
	top: -5px
}

.module-card-item-footer .icon-btn {
	width: auto
}

.module-card-item-footer .play-btn-o {
	padding: 0 15px
}

.module-card-item-footer .play-btn-o::after {
	border-radius: 50px
}

.search-box {
	flex: 1
}

.searchbar-main {
	width: 100%;
	max-width: 580px;
	height: 40px;
	position: relative
}

.searchbar {
	transition: background .2s ease;
	background: #e3e6ea;
	height: 40px;
	border-radius: 8px
}

.searchbar-main.open .searchbar,
.searchbar:hover {
	background: #fff;
	position: relative;
	z-index: 9
}

.search-input {
	height: 100%;
	width: 100%;
	padding: 0 15px;
	flex: 1;
	display: flex
}

.search-btn {
	height: 100%;
	padding: 0 15px
}

.search-btn i {
	font-size: 16px;
	vertical-align: -2px
}

.search-recommend-box {
	display: none;
	width: 100%;
	position: absolute
}

.searchbar-main.open .search-recommend-box {
	display: inline-block
}

.search-recommend {
	position: relative;
	z-index: -1;
	width: 97%;
	left: 1.5%;
	font-size: 0
}

.search-recommend-title {
	font-size: 14px;
	color: #757575;
	padding-bottom: 5px
}

.searchbar-main.open .search-recommend {
	position: absolute;
	background: #fff
}

.search-tag {
	font-size: 0;
	margin: 0 -5px
}

.search-tag a {
	color: #424242;
	background: #e9ecef;
	padding: 0 15px;
	margin: 5px
}

.search-tag a:hover {
	background: #e3e6ea
}

.search-tag a:hover,
.search-tag .hot {
	color: #e50914
}

.search-tag .hot {
	background: #ffeae5
}

.search-tag .hot:hover {
	background: #ffddd5
}

.search-tag i {
	margin-right: 5px;
	vertical-align: -2px;
	font-size: 14px
}

.cancel-btn {
	display: none
}

.navbar-item i.icon-arrow-go {
	transition: all .2s ease;
	float: right;
	line-height: 40px;
	font-size: 12px;
	color: transparent;
	margin-right: 20px
}

.navbar-item.active a {
	color: #e50914;
	font-weight: 700
}

.tab-list {
	display: none
}

.tab-list.active {
	display: block
}

@keyframes opacity {
	0% {
		opacity: 100%
	}

	50% {
		opacity: 30%
	}

	100% {
		opacity: 100%
	}
}

@-webkit-keyframes opacity {
	0% {
		opacity: 100%
	}

	50% {
		opacity: 30%
	}

	100% {
		opacity: 100%
	}
}

.module-class::after {
	border-radius: 0
}

.module-class-items {
	padding: 0;
	margin: 0;
	display: flex
}

.module-class-items:last-child::after,
.module-class-items:last-child .module-class-item::after {
	height: 0
}

.module-class-item {
	display: inline-flex;
	padding: 5px 0
}

.module-item-title {
	position: relative;
	font-size: 16px;
	font-weight: 700;
	display: inline-block;
	width: 45px;
	white-space: nowrap;
	min-height: 40px;
	line-height: 44px;
	margin-right: 20px
}

.module-item-title i {
	margin-left: 10px;
	vertical-align: initial;
	font-size: 12px;
	transform: scale(0.6) rotate(270deg);
	color: #c2c6d0
}

.module-item-box {
	display: inline-block;
	margin-left: -5px
}

.module-item-box a {
	padding: 6px 12px;
	font-size: 14px;
	margin: 5px;
	white-space: nowrap;
	display: inline-block;
	border-radius: 8px
}

.module-item-box .active {
	color: #e50914;
	background: #ffddd5;
	font-weight: 700
}

.module-item-box .active::after {
	border-color: #ffddd5
}

.module-page {
	margin: 30px 0 0
}

.module-ranking {
	margin-bottom: 30px
}

.module-more,
.module-footer {
	text-align: center;
	border-top: 2px solid #e9ecef;
	margin-top: 10px
}

.view .module {
	padding-bottom: 40px
}

.module-info-heading h1 {
	font-size: 30px;
	line-height: 1.38;
	margin-bottom: 10px;
	position: relative
}

.module-info-tag-link {
	background: #fff;
	margin-right: 6px;
	padding: 0 12px;
	font-size: 14px
}

.module-info-tag-link span,
.module-info-tag-link a {
	color: #424242;
	cursor: pointer
}

.module-info-tag-link span:hover,
.module-info-tag-link a:hover {
	color: #e50914
}

.module-info-tag-link .slash,
.module-info-tag-link .slash:hover {
	color: #bcbcbc;
	cursor: auto
}

.module-info-tag-link:last-child {
	margin: 0
}

.module-info-items {
	padding: 20px 25px;
	position: relative;
	z-index: 10
}

.module-info-item {
	font-size: 14px;
	display: flex;
	padding-bottom: 5px;
	word-break: break-all
}

.module-info-item-content span:nth-child(2n-1) {
	cursor: pointer
}

.module-info-item-content span:nth-child(2n-1):hover {
	color: #e50914
}

.module-info-item-link::after {
	content: '、';
	color: #9e9e9e;
	margin-right: 2px;
	font-weight: 700
}

.module-info-item-link:last-child::after {
	content: ''
}

.module-info-item-title {
	display: inline-block;
	min-width: 46px;
	font-weight: 700
}

.module-info-introduction {
	margin-bottom: 15px;
	padding-bottom: 15px
}

.module-info-introduction-content {
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	word-wrap: break-word;
	display: -webkit-box;
	overflow: hidden;
	max-height: 44px
}

.module-list {
	background: #fff
}

.module-play-list {
	margin: -5px;
	max-height: 380px;
	overflow-y: auto
}

.module-play-list-link {
	cursor: pointer;
	color: #424242;
	margin: 5px;
	padding: 0px 10px;
	text-align: center;
	position: relative
}

.module-play-list-link.active {
	color: #e50914;
	font-weight: 700;
	background: #fff0ec !important
}

.module-play-list-link:hover {
	background: #fff0ec !important;
	color: #e50914
}

.module-play-list-link:hover::after {
	border: none
}

.module-play-list-link.active::after {
	border: none
}

.module-play-list-base .module-play-list-link {
	width: calc(10% - 10px)
}

.module-play-list-large .module-play-list-link {
	width: calc(14.2857% - 10px)
}

.module-play-list-larger .module-play-list-link {
	padding: 3px 20px;
	min-width: calc(20% - 10px)
}


.player-box-main {
	position: relative;
	overflow: hidden
}

.MacPlayer iframe {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border: 0
}

.MacPlayer {
	padding-bottom: 56.25% !important;
	display: block
}

.module-player {
	padding-bottom: 40px
}

.handle-btn {
	font-size: 14px;
	text-align: center;
	cursor: pointer
}

.module-player-handle-item.next {
	display: none
}

.prompt .main {
	padding-bottom: 50px;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	text-align: center
}

.module-prompt {
	height: 500px;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	text-align: center
}

.module-prompt-pic {
	height: 150px;
	padding-bottom: 20px
}

.module-prompt-pic img {
	height: 100%;
	width: 100%
}

.module-prompt a {
	color: #757575
}

.module-prompt p {
	margin-top: 60px
}

.module-prompt-info {
	color: #757575;
	font-size: 14px;
	padding: 0 30px;
	width: 100%
}

.module-prompt-info-bg {
	position: relative;
	padding: 30px
}

.module-prompt-info-bg::after {
	content: '';
	position: absolute;
	height: 120px;
	width: 600px;
	background: linear-gradient(to bottom, #e9ecef 0%, #f7f8f9 50%);
	bottom: 110px;
	left: calc(50% - 300px);
	z-index: -1;
	border-radius: 50%
}

.module-jump-prompt {
	height: 80vh;
	width: 450px;
	margin: 0 auto;
	background: #fff url(../images/colour-bg.png) no-repeat;
	background-size: 150%;
	background-position-y: -30px;
	box-shadow: 0px 10px 50px rgba(0, 0, 0, 0.07);
	border-radius: 18px;
	position: relative;
	top: 5vh;
	left: 0
}

.module-jump-prompt::before {
	content: '';
	position: absolute;
	left: calc(50% - 24px);
	bottom: -80px;
	width: 48px;
	height: 48px;
	background: url(../images/logo-o.svg) no-repeat;
	z-index: -1;
	background-size: contain
}

.module-jump-icon {
	height: 100px;
	width: 170px;
	margin: 0 auto 30px;
	background: url(../images/jump.svg) no-repeat 20px;
	background-size: contain
}

.spinner {
	width: 60px;
	height: 18px;
	position: relative;
	margin: 0 auto 30px;
	font-size: 14px;
	text-align: center
}

.k-loader {
	display: inline-block;
	font-size: 2em;
	margin: 0 .5em 0 .2em;
	position: relative;
	height: .5em;
	width: 1em
}

.k-loader:before,
.k-loader:after {
	content: "";
	display: block;
	height: .5em;
	position: absolute;
	width: .5em;
	border-radius: 50px
}

.k-loader:before {
	animation: k-loadingK 1.2s ease-in-out infinite, k-loadingM .6s ease-in-out infinite;
	background: #e50914;
	color: #fff;
	background: linear-gradient(to right, #ff711f 0%, #e50914 100%)
}

.k-loader:after {
	animation: k-loadingL 1.2s ease-in-out infinite, k-loadingN .6s ease-in-out infinite;
	background: #fcd877;
	background: linear-gradient(90deg, #f9f2df, #fcd877) !important
}

.fixedGroup {
	position: fixed;
	right: 0;
	bottom: 85px;
	width: 34px;
	z-index: 10;
	box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
	box-sizing: border-box;
	border-radius: 4px;
	background-color: #fff;
}

.fixedGroup .fixedGroup-item {
	position: relative;
	font-size: 20px;
	display: flex;
	justify-content: center;
	align-items: center;
	line-height: 36px;
	height: 36px;
	cursor: pointer;
}

.fixedGroup .retop {
	display: none;
}

.fixedGroup .fixedGroup-item i {
	display: block;
	padding: 4px;
	box-sizing: border-box;
	width: 28px;
	height: 28px;
	border-radius: 4px;
}

.fixedGroup .fixedGroup-item::after {
	content: "";
	position: absolute;
	right: 50%;
	bottom: 0;
	width: 20px;
	height: 1px;
	background-color: #0c0d0f0f;
	transform: translateX(50%);
}

.fixedGroup .fixedGroup-item:last-child::after {
	content: none;
}

.fixedGroup .fixedGroup-item:hover i {
	background-color: rgb(245, 245, 245);
}

.fixedGroup .fixedGroup-item .fixedGroup__cell {
	display: none;
	font-size: 15px;
	position: absolute;
	top: 2px;
	right: 54px;
	padding: 0 12px;
	color: #fff;
	font-size: 14px;
	border-radius: 2px;
	background-color: #4d4d4d;
	min-width: 52px;
	min-height: 32px;
	line-height: 32px;
	animation: fadeInOut .2s ease-out 0s forwards;
	transform: all .2s;
	white-space: nowrap;
}

.fixedGroup .fixedGroup-item:hover .fixedGroup__cell {
	display: block;
}

@keyframes fadeInOut {
	0% {
		transform: scale(.5);
	}

	100% {
		transform: scale(1);
	}
}

.fixedGroup .fixedGroup-item .fixedGroup__cell .fixedGroup__triangle {
	position: absolute;
	top: 12px;
	right: -5px;
	width: 10px;
	height: 10px;
	background-color: #4d4d4d;
	border-top-right-radius: 2px;
	transform: rotate(45deg);
}

@keyframes k-loadingK {
	0% {
		left: 0;
		transform: scale(1.2)
	}

	50% {
		left: 100%;
		transform: scale(1)
	}

	100% {
		left: 0;
		transform: scale(1.2)
	}
}

@keyframes k-loadingL {
	0% {
		left: 100%;
		transform: scale(1.2)
	}

	50% {
		left: 0;
		transform: scale(1)
	}

	100% {
		left: 100%;
		transform: scale(1.2)
	}
}

@keyframes k-loadingM {
	0% {
		z-index: 0
	}

	50% {
		z-index: 10
	}

	100% {
		z-index: -2
	}
}

@keyframes k-loadingN {
	0% {
		z-index: 1
	}

	50% {
		z-index: -2
	}

	100% {
		z-index: 10
	}
}

#page {
	padding: 15px 0;
	font-size: 0;
	text-align: center;
}

.page-link {
	line-height: 34px;
	display: inline-block;
	font-size: 14px;
	border-radius: 8px;
	background: #e9ecef;
	min-width: 34px;
	position: relative;
	margin: 5px
}

.page-current:hover,
.page-current {
	background: #e50914;
	color: #fff;
	background: linear-gradient(to right, #ff711f 0%, #e50914 100%);
	box-shadow: 0 10px 12px -4px rgba(229, 9, 20, .25);
	font-weight: 700;
}

.page-previous,
.page-next {
	padding: 0 18px
}

.page-more {
	background: #e9ecef;
	width: 180px;
	margin-top: -15px;
	border-radius: 0 0 18px 18px
}

.open~.shortcuts-mobile-overlay {
	background-color: rgba(57 61 73 / 80%);
	pointer-events: auto;
	z-index: 99
}

.none {
	display: none;
}

.popupShow {
	display: block !important;
}

.popup {
	box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05), 0 1.5rem 2.2rem rgba(0, 0, 0, 0.1) !important;
	overflow: hidden;
	padding: 0 30px;
	background: #fff;
	width: 400px;
	position: fixed;
	top: 50%;
	left: 50%;
	z-index: 999999;
	transform: translateX(-50%) translateY(-50%);
	margin: 0 auto;
	border-radius: 18px
}

.popup::after {
	content: '';
	height: 150px;
	width: 120%;
	background: #f9d342;
	position: absolute;
	bottom: -95px;
	left: -10%;
	z-index: -1;
	border-radius: 50%
}

.popup-header {
	text-align: center;
	padding: 25px 0 10px
}

.popup-title {
	position: relative;
	font-size: 20px;
	font-weight: 900;
	display: inline-block
}

.popup-title::before {
	content: '';
	position: absolute;
	width: 90px;
	height: 15px;
	left: -5px;
	background: #ffddd5;
	bottom: 3px;
	border-radius: 4px;
	transform: skewX(-15deg);
	z-index: -1
}

.popup-main {
	padding-bottom: 10px
}

.popup-main p {
	padding: 2px 0 0
}

.popup-main .pc {
	padding: 10px 0
}

.popup-main p a {
	color: #e50914
}

.popup-main p:last-child {
	padding-top: 15px;
	margin-top: 15px
}

.popup-main p:last-child::after {
	background: #e3e6ea
}

.popup-main p img {
	height: 50%;
	width: 50%;
	display: block;
	margin: 0 auto
}

.popup-footer {
	padding: 10px 0 30px;
	text-align: center
}

.popup-btn {
	cursor: pointer
}

.shortcuts-box {
	position: fixed;
	left: 0;
	top: 0;
	z-index: 10;
	display: none;
	width: 100%;
	height: 100%
}

#shortcuts-info {
	position: absolute;
	width: 280px;
	height: auto;
	padding: 25px;
	top: 50%;
	left: 50%;
	margin: -50px 0 0 -140px;
	text-align: center;
	color: #fff;
	border: 2px solid #fff;
	background-color: #0c0d0f;
	border-radius: 8px
}

.skeleton-bg,
.skeleton .module-poster-item-title,
.skeleton .module-card-item-title,
.skeleton .module-info-item span,
.skeleton .module-card-item-footer span,
.skeleton.module-play-list-link {
	background: linear-gradient(90deg, #e9ecef 25%, #e3e6ea 37%, #e9ecef 63%);
	background-size: 400% 500%;
	border-radius: 8px;
	animation: skeleton-loading 1.4s ease infinite
}

.skeleton .module-item-cover {
	background: linear-gradient(90deg, transparent 25%, #e3e6ea 37%, transparent 63%);
	background-size: 400% 500%;
	border-radius: 8px;
	animation: skeleton-loading 1.4s ease infinite
}

.skeleton .module-item-cover::after {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	background: url(../images/loading.jpg) no-repeat center;
	background-size: 100%;
	z-index: -1
}

.skeleton .module-poster-item-title {
	height: 22px
}

.skeleton .module-poster-item .module-item-cover:hover {
	box-shadow: none
}

.skeleton .module-card-item-class {
	width: 38px
}

.skeleton .module-card-item-title {
	height: 18px;
	border-radius: 4px
}

.skeleton .module-info-item span {
	height: 14px;
	width: 30px;
	margin: 3px 3px 3px 0;
	border-radius: 4px
}

.skeleton .module-card-item-footer span {
	border-radius: 50px;
	height: 30px;
	width: 60px;
	top: -15px;
	position: relative;
	z-index: 1;
	margin-left: 20px;
	display: inline-block
}

.skeleton.module-play-list-link {
	height: 34px
}

.skeleton.module-play-list-link:hover {
	background: #e9ecef !important
}

.skeleton.module-play-list-link::after {
	border-color: transparent
}

.module-play-list-larger .skeleton.module-play-list-link {
	height: 40px
}

@keyframes skeleton-loading {
	0% {
		background-position: 100% 50%
	}

	100% {
		background-position: 0 50%
	}
}

.player-rm {
	padding: 15px 10px;
	background: #000;
	position: relative;
	z-index: 10
}

.rm-list {
	font-size: 0
}

.rm-list img {
	display: block;
	width: 100%;
	border-radius: 8px
}

.rm-two a {
	margin: 0 5px;
	display: inline-block;
	width: calc(50% - 10px)
}


.app-module {
	padding: 40px 0 80px;
	position: relative
}

.app-module::after {
	content: '';
	height: 100%;
	width: 100%;
	position: absolute;
	left: 0;
	top: -30px;
	background: url(../images/app-bg.png) no-repeat;
	background-size: 80%;
	z-index: -2
}

.app-info {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center
}

.app-title {
	font-size: 48px
}

.app-desc {
	font-size: 16px;
	color: #9e9e9e
}

.app-downlist {
	padding-top: 25px
}

.app-downlist .app-downlist-go {
	width: 200px;
	text-align: center
}

.app-downlist .app-downlist-go.disable {
	cursor: not-allowed;
	background: #c2c6d0;
	filter: grayscale(100%)
}

.app-downlist .app-downlist-go:hover.disable::after {
	height: 0
}

.app-pic {
	position: relative;
	padding: 20px 0;
	overflow: hidden;
	display: flex;
	align-items: center;
	justify-content: center;
	background: url(../images/phone.png) no-repeat center;
	height: 500px;
	width: calc(100% + 80px);
	margin: 80px -40px 0;
	background-size: contain
}

.app-pic::before,
.app-pic::after {
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	background: rgba(247, 248, 249, 0.58);
	width: calc(50% - 123px);
	z-index: -1
}

.app-pic::before {
	left: 0
}

.app-pic::after {
	right: 0
}

.app-pic-list {
	animation: move 180s linear infinite;
	position: relative;
	z-index: -2;
	top: 15px;
	height: 100%;
	width: 218%;
	white-space: nowrap;
	display: flex
}

.app-pic-list-poster {
	height: 100%;
	width: 100%
}

.app-pic-list img {
	height: 100%;
	display: block
}


@keyframes move {
	0% {
		transform: translateX(0px)
	}

	100% {
		transform: translateX(-100%)
	}
}

.app-qrcode {
	height: 180px;
	width: 180px;
	border-radius: 18px;
	background: #fff;
	overflow: hidden;
	/*padding: 6px;*/
	position: absolute;
	/*left: calc(50% - 100px);*/
	bottom: 45px
}

.app-qrcode img {
	height: 100%
}



.links-list {
	border-top: 2px solid #f1f3f5;
	position: relative;
	padding: 25px 0;
	margin-top: 20px;
	word-break: break-word;
	font-size: 0
}

.links-list .module-heading {
	display: inline-block;
	background: #f7f8f9;
	padding-right: 15px;
	position: absolute;
	left: 0;
	top: -13px;
	margin: 0
}

.links-list h3 {
	font-size: 16px
}

.links-list-go {
	font-size: 14px;
	margin: 0 10px 10px 0
}

.links-list-go:hover {
	text-decoration: underline
}

.footer-content {
	background: #e9ecef;
	margin: 0 -40px;
	padding: 20px 40px
}

.foot-logo {
	height: 28px
}

.foot-logo img {
	display: block;
	height: 100%
}

.foot-copyright {
	flex: 1;
	text-align: right
}

.foot-copyright a {
	margin-left: 20px;
	font-size: 13px;
	position: relative
}

.foot-copyright a::before {
	content: '/';
	position: absolute;
	left: -13px;
	color: #c2c6d0;
	bottom: -5%;
	font-size: 12px;
	font-weight: 400
}

.foot-copyright a:first-child::before {
	content: ''
}

.foot-copyright p {
	font-size: 12px;
	margin-top: 3px;
	color: #757575
}

@media (max-width: 4096px) {
	.module-poster-items-aline .module-poster-item:nth-last-child(-n+2) {
		display: none
	}
}

@media (max-width: 1789px) {
	.module-poster-items-small .module-poster-item:last-child {
		display: none
	}

	.module-poster-items-aline .module-poster-item:nth-last-child(-n+3),
	.module-poster-items-base .module-poster-item:nth-last-child(-n+2) {
		display: none
	}

	.view .module-poster-items-base .module-poster-item,
	.player .module-poster-items-base .module-poster-item {
		display: inline-block !important
	}

	.view .module-poster-items-base .module-poster-item:nth-child(n+15),
	.player .module-poster-items-base .module-poster-item:nth-child(n+15) {
		display: none !important
	}

	.module-poster-item {
		width: calc(14.285714% - 18px)
	}

	.module-card-item {
		width: calc(33.33% - 18px)
	}

	.module-title {
		font-size: 28px
	}
}

@media (max-width: 1549px) {
	.module-poster-item {
		width: calc(16.666667% - 18px)
	}

	.module-poster-items-small .module-poster-item:nth-last-child(-n+2) {
		display: none
	}

	.module-poster-items-aline .module-poster-item:nth-last-child(-n+4) {
		display: none
	}

	/*.module-poster-items-base .module-poster-item:nth-last-child(-n+4) {*/
	/*	display: none*/
	/*}*/
	.module-poster-items-base .module-poster-item:nth-child(n+13) {
		display: none
	}

	.view .module-poster-items-base .module-poster-item:nth-child(n+13),
	.player .module-poster-items-base .module-poster-item:nth-child(n+13) {
		display: none !important
	}

	.module-play-list-base .module-play-list-link {
		width: calc(12.5% - 10px)
	}

	.module-play-list-large .module-play-list-link {
		width: calc(20% - 10px)
	}

	.module-play-list-larger .module-play-list-link {
		min-width: calc(25% - 10px);
		padding: 3px
	}
}

@media (max-width: 1369px) {
	.module-card-item {
		width: calc(50% - 18px)
	}
}

@media (min-width: 1025px) {

	.view .module-title,
	.player .module-title {
		font-size: 24px
	}

	.view .module-heading.player-heading {
		height: 33px
	}

	.module-title a:hover {
		color: #e50914
	}

	.module-title a:hover .module-title-en,
	.module-title a:hover .module-ranking-tab-info {
		filter: grayscale(100%)
	}

	.module-poster-item .module-item-cover:hover {
		box-shadow: 0 20px 15px -10px rgba(0, 0, 0, 0.2), 0 50px 35px -40px rgba(0, 0, 0, 0.4), 0 50px 35px -40px rgba(243, 2, 2, 0.2)
	}

	.sidebar {
		width: 200px;
		padding: 64px 15px 0
	}

	.main,
	.footer {
		margin-left: 200px;
		padding-left: 40px;
		padding-right: 40px
	}

	.header {
		margin: 0 0 0 200px
	}

	.domain {
		position: absolute;
		bottom: 20px;
		width: 100%;
		left: 0
	}

	.domain img {
		width: 76%;
		margin-left: 12%
	}

	.navbar-item {
		font-size: 16px
	}

	.player-heading {
		margin-bottom: 17px
	}

	.player-heading .module-tab {
		margin: -8px 0 -25px
	}

	.player-list {
		height: 100%
	}

	.player-box-main {
		position: relative;
		overflow: hidden;
		display: inline-block;
		z-index: 10;
		width: calc(100% - 360px)
	}

	.player-box {
		font-size: 0;
		position: relative;
		background: #fff;
		border-radius: 0 18px 18px 0
	}

	.player-box::after {
		content: '';
		width: 100%;
		height: 100%;
		position: absolute;
		left: 0;
		top: 0;
		border-radius: 0 18px 18px 0;
		z-index: 9;
		box-shadow: 0 2.75px 2.21px rgba(0, 0, 0, 0.01), 0 6.65px 5.32px rgba(0, 0, 0, 0.02), 0 12.5px 10px rgba(0, 0, 0, 0.01), 0 22px 18px rgba(0, 0, 0, 0.03), 0 42px 33.4px rgba(0, 0, 0, 0.02), 0 100px 80px rgba(0, 0, 0, 0.01), 0 0 1px rgba(0, 0, 0, 0.1)
	}

	.module-player-side {
		position: absolute;
		right: 0px;
		top: 0;
		width: 355px;
		height: 100%;
		bottom: 0;
		z-index: 10;
		border-top-right-radius: 18px;
		border-bottom-right-radius: 18px;
	}

	.module-player-info {
		padding: 20px;
		border-top-right-radius: 18px;
		position: relative;
		background: #e9ecef
	}

	.module-player-info .module-info-heading {
		margin: 0;
		position: relative
	}

	.module-player-info .module-info-heading h1 {
		font-size: 20px;
		position: initial;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		word-wrap: break-word;
		display: -webkit-box;
		overflow: hidden;
		max-height: 54px
	}

	.module-player-info .module-info-heading h1::before {
		left: -20px;
		height: 28px
	}

	.module-player-info .module-info-tag-link {
		background: #f7f8f9
	}

	.module-player-info .module-info-tag-link::after {
		border-color: #e3e6ea
	}

	.speed:hover .speed-prompt {
		display: block
	}

	.player .module-play-list-content {
		padding-bottom: 20px
	}

	.player .module-player .module-title {
		font-size: 14px;
		flex: 1;
	}

	.player .player-heading {
		margin: 0;
		padding: 0 20px;
		background: #e9ecef
	}

	.player .player-heading .module-tab {
		margin: 0 0 -1px;
		flex: 3;
	}

	.player .player-heading .module-tab-items {
		padding: 0;
		width: 100%;
	}

	.player .module-list {
		box-shadow: none;
		background: transparent;
		height: calc(100% - 205px);
		overflow: auto;
		border-radius: 0
	}

	.player .module-list.active {
		border-top: 1px solid #e3e6ea
	}

	.player .module-play-list {
		max-height: initial
	}

	.player .module-play-list-base .module-play-list-link {
		width: calc(33.33% - 10px)
	}

	.player .module-play-list-large .module-play-list-link {
		width: calc(50% - 10px)
	}

	.player .module-play-list-larger .module-play-list-link {
		min-width: calc(100% - 10px)
	}

	.player .module-tab-items {
		background: transparent
	}

	.player .player-heading .module-tab-item.active {
		position: relative;
		z-index: 1;
		border-radius: 8px 8px 0 0;
		background: #fff;
		box-shadow: none;
		border: 1px solid #e3e6ea;
		border-bottom-color: #fff
	}

	.player .player-heading .module-tab-items-box {
		padding: 0
	}

	.player .player-heading .module-tab-item {
		line-height: 40px;
		padding: 0 12px
	}

	.module-player-handle-items {
		position: absolute;
		bottom: 0;
		width: 100%;
		height: 60px;
		z-index: 9;
		display: flex;
		align-items: center;
		border-top: 1px solid #f3f5f7;
		background: #fafafa;
		border-bottom-right-radius: 18px
	}

	.module-player-handle-item {
		font-size: 0;
		display: inline-block;
		flex: 1;
		position: relative;
		border-left: 3px solid #e3e6ea
	}

	.module-player-handle-item:first-child {
		border: none
	}

	.handle-btn {
		font-weight: 700;
		line-height: 15px
	}

	.module-player-handle-item:hover .handle-btn {
		color: #e50914
	}

	.play-btn.icon-btn span {
		display: none
	}

	.module-card-item-footer .icon-btn {
		padding: 0 8px 0 3px;
		margin-right: -15px
	}

	.module-card-item-footer .icon-btn,
	.module-card-item-footer .icon-btn::after {
		border-radius: 50px 18px 18px 50px
	}

	.module-card-item-footer .icon-btn::before {
		border-radius: 50px 25px 25px 50px
	}

	.module-card-item-footer .icon-btn+.play-btn-o {
		padding: 0 15px 0 13px
	}

	.module-card-item-footer .icon-btn+.play-btn-o,
	.module-card-item-footer .icon-btn+.play-btn-o::after {
		border-radius: 18px 50px 50px 18px
	}

	.module-card-item-footer .icon-btn+.play-btn-o::before {
		border-radius: 25px 50px 50px 25px
	}

	.handle-btn-icon {
		margin-right: 10px
	}

	.handle-btn-icon i {
		vertical-align: -1px
	}

	.drop-qrcode {
		top: inherit;
		bottom: 50px;
		width: 330px;
		right: 15px
	}

	.drop-qrcode-content {
		padding: 20px;
		font-size: 0
	}

	.drop-qrcode-info {
		text-align: center
	}

	.drop-qrcode-img {
		padding: 30px 0 15px
	}

	.drop-qrcode-img canvas {
		height: 160px;
		width: 160px;
		margin: 0 auto
	}

	.drop-qrcode-info-text {
		font-size: 14px;
		padding-left: 15px
	}

	.drop-qrcode-info-text p {
		color: #757575
	}

	.drop-qrcode-info-text p strong {
		font-weight: 700;
		color: #212121
	}

	.drop-qrcode-info-tips {
		font-size: 12px;
		margin: 15px 0 0;
		padding: 10px 12px;
		background: #fff0ec;
		border-radius: 8px
	}

	.player-rm {
		width: calc(100% - 360px);
		border-bottom-left-radius: 18px
	}
}

@media (max-width: 1024px) {
	.domain {
		display: none
	}

	.module-paper-item {
		width: calc(33.33% - 18px)
	}

	.module-paper-item:last-child {
		display: none
	}

	.module-poster-item {
		width: calc(20% - 18px)
	}

	.module-poster-items-small .module-poster-item:nth-last-child(-n+3) {
		display: none
	}

	.module-poster-items-aline .module-poster-item:nth-last-child(-n+4) {
		display: inline-block
	}

	.module-poster-items-base .module-poster-item:nth-last-child(-n+4) {
		display: inline-block
	}

	/*.module-poster-items-base .module-poster-item:nth-last-child(-n+1) {*/
	/*	display: none*/
	/*}*/
	.module-poster-items-base .module-poster-item:nth-child(n+16) {
		display: none
	}

	.view .module-poster-items-base .module-poster-item:nth-child(n+13),
	.player .module-poster-items-base .module-poster-item:nth-child(n+13) {
		display: inline-block !important
	}

	.view .module-poster-items-base .module-poster-item:nth-child(n+16),
	.player .module-poster-items-base .module-poster-item:nth-child(n+16) {
		display: none !important
	}

	.module-paper-item-main a:hover {
		box-shadow: initial
	}

	.module-item-top {
		font-size: 24px;
		text-indent: 8px
	}

	.module-item-top::after {
		left: -120%;
		top: -50%
	}

	.module-heading-tab .module-heading-tab-link.active::after {
		bottom: -75%
	}

	.module-heading-tab-link,
	.module-title {
		font-size: 24px
	}

	.module-title-en,
	.module-ranking-tab-info {
		font-size: 30px
	}

	.module-heading-tab .line {
		height: 20px
	}

	.module-heading-search-result {
		font-size: 18px
	}

	.module-info-poster {
		width: 180px !important
	}

	.player-rm {
		border-radius: 0;
		padding: 10px 5px
	}

	.module-player-info {
		background: #fff;
		border-radius: 0 0 18px 18px;
		position: relative;
		padding: 20px 0 25px;
		margin-bottom: 40px
	}

	.module-player-info::after {
		content: '';
		width: 100%;
		height: 100%;
		position: absolute;
		left: 0;
		top: 0;
		border-radius: 18px;
		z-index: 1;
		box-shadow: 0 2.75px 2.21px rgba(0, 0, 0, 0.01), 0 6.65px 5.32px rgba(0, 0, 0, 0.02), 0 12.5px 10px rgba(0, 0, 0, 0.01), 0 22px 18px rgba(0, 0, 0, 0.03), 0 42px 33.4px rgba(0, 0, 0, 0.02), 0 100px 80px rgba(0, 0, 0, 0.01), 0 0 1px rgba(0, 0, 0, 0.1)
	}

	.module-player-info .module-info-heading {
		margin: 0 0 0 25px;
		max-width: 75%
	}

	.module-player-handle-item.qrcode {
		display: none
	}

	.module-player-handle-item.next {
		display: inline-block
	}

	.module-card-item-footer a {
		font-size: 12px
	}

	.module-card-item-footer .play-btn.icon-btn {
		text-indent: initial;
		margin-right: -5px;
		padding: 0 15px
	}

	.module-card-item-footer .play-btn i {
		margin-right: 5px
	}

	.module-card-item-footer a span {
		font-weight: 700
	}

	.module-card-item-footer .play-btn.icon-btn span {
		position: relative;
		top: -2%
	}

	.module-player-side {
		position: relative
	}

	.module-player-handle-items {
		position: absolute;
		top: 25px;
		right: 25px;
		z-index: 9
	}

	.module-player-handle-item {
		display: inline-block;
		margin-left: 10px
	}

	.handle-btn {
		border-radius: 8px;
		height: 70px;
		width: 70px;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		background: #f7f8f9
	}

	.handle-btn:hover {
		background: #e9ecef
	}

	.handle-btn-icon {
		font-size: 18px
	}

	.handle-btn-name {
		font-size: 12px
	}

	.app-title {
		font-size: 38px
	}

	.app-desc {
		font-weight: 400
	}

	.app-module::after {
		background-size: 180%;
		background-position-x: -180px
	}
}

@media (min-width: 900px) {
	.header .header-box {
		margin: 0 40px
	}
}

@media (max-width: 899px) {
	.header-op-list-btn span {
		display: none
	}

	.header-op-list-btn {
		padding: 0;
		width: 35px;
		text-align: center
	}

	.header-op-list-btn i {
		margin: 0;
		font-size: 18px;
		height: 18px;
		width: 18px;
		vertical-align: text-bottom;
		font-weight: 900
	}

	.module-items {
		margin-right: -15px
	}

	.module-item {
		margin: 0 15px 18px 0
	}

	.module-heading-tab {
		justify-content: center
	}

	.module-poster-item {
		width: calc(25% - 15px)
	}

	.module-card-item {
		width: calc(100% - 15px);
		margin-bottom: 40px !important
	}

	.module-poster-items-small .module-poster-item:nth-last-child(-n+3) {
		display: inline-block
	}

	.module-poster-items-aline .module-poster-item:nth-last-child(-n+2) {
		display: none
	}

	/*.module-poster-items-base .module-poster-item:nth-last-child(-n+4) {*/
	/*	display: none*/
	/*}*/
	.module-poster-items-base .module-poster-item:nth-child(n+13) {
		display: none
	}

	.view .module-poster-items-base .module-poster-item:nth-child(n+16),
	.player .module-poster-items-base .module-poster-item:nth-child(n+16) {
		display: inline-block !important
	}

	.module-play-list-base .module-play-list-link {
		width: calc(20% - 10px)
	}

	.module-play-list-large .module-play-list-link {
		width: calc(33.3333% - 10px)
	}

	.module-play-list-larger .module-play-list-link {
		min-width: calc(33.3333% - 10px)
	}

	.module-paper-item {
		width: calc(50% - 15px)
	}

	.module-paper-item:nth-last-child(-n+2) {
		display: none
	}

	.module-item-note {
		font-size: 10px
	}

	.module-poster-items-small .module-item-note {
		padding: 3px 7px 2px;
		border-radius: 8px -2px
	}

	.module-info-poster {
		width: 140px !important;
		margin: 0 15px 0 15px !important
	}

	.module-info-heading h1 {
		font-size: 24px
	}

	.module-player-info .module-info-heading {
		max-width: 65%
	}

	.app-module::after {
		background-size: 130%
	}

	.foot-logo {
		display: none
	}

	.foot-copyright {
		text-align: center;
		padding: 0 10px
	}

	#friendlink {
		display: none
	}
}

@media (min-width: 560px) {
	#mobile-tab-box {
		display: none !important;
	}

	.sidebar {
		height: 100vh;
		background: #fff;
		border-right: 1px solid #e9ecef
	}

	.main {
		padding-top: 94px;
		min-height: calc(100vh - 175px)
	}

	.header {
		z-index: 11;
		background: rgba(247, 248, 249, 0.97);
		-webkit-backdrop-filter: saturate(180%) blur(10px)
	}

	.header .header-box {
		border-radius: 0;
		box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05)
	}

	.header-op-list-btn i {
		vertical-align: text-bottom
	}

	.searchbar {
		border-radius: 8px
	}

	.searchbar-main.open .search-recommend {
		box-shadow: 0px 0 10px 0 rgba(0, 0, 0, 0.1);
		padding: 20px;
		border-radius: 0 0 8px 8px
	}

	.navbar-item {
		padding: 0 16px;
		height: 40px;
		line-height: 39px;
		display: flex;
		align-items: center
	}

	.navbar-item.active::before,
	.navbar-item.active .links::before {
		border: 1px solid #e9ecef
	}

	.navbar-item.active {
		margin: 0 -16px 0 0;
		padding-left: 16px;
		background: linear-gradient(to right, #fff 0%, #e9ecef 90%, #f7f8f9 90%)
	}

	.navbar-item.active a {
		background: linear-gradient(to right, #fff 0%, #f7f8f9 90%)
	}

	.navbar-item.active::before {
		border-width: 0 1px 1px 0
	}

	.navbar-item.active .links::before {
		border-width: 1px 1px 0 0
	}

	.navbar-item.active .icon-home-o:before {
		content: "\e909"
	}

	.navbar-item.active .icon-dy-o:before {
		content: "\e905"
	}

	.navbar-item.active .icon-tv-o:before {
		content: "\e90b"
	}

	.navbar-item.active .icon-dm-o:before {
		content: "\e907"
	}

	.navbar-item.active .icon-zy-o:before {
		content: "\e903"
	}

	.navbar-item.active .icon-update-o:before {
		content: "\e90f"
	}

	.navbar-item.active .icon-ranking-o:before {
		content: "\e90d"
	}

	.navbar-item.active .icon-jl-o:before {
		content: "\e913"
	}

	.navbar-item.active .icon-phone-o:before {
		content: "\e974"
	}

	.navbar-item.active .icon-week-o:before {
		content: "\e975"
	}

	.navbar-hr {
		margin: 10px 0;
		padding: 0;
		height: 1px;
		background: #f7f8f9
	}

	.navbar-item a {
		display: inline-block;
		width: 100%;
		height: calc(100% - 2px)
	}

	.navbar-item span {
		margin-left: 10px
	}

	.navbar-item i {
		vertical-align: text-bottom;
		width: 19px;
		text-align: center
	}

	.navbar-item i.icon-arrow-go {
		float: right;
		line-height: 40px;
		font-size: 12px;
		color: transparent;
		margin-right: 20px
	}

	.navbar-item:hover i.icon-arrow-go {
		margin: 0;
		color: #e50914
	}

	.navbar-item.active i.icon-arrow-go {
		display: none
	}

	.module-tab {
		white-space: nowrap;
		overflow: auto;
		max-width: 75%
	}

	.module-tab-items {
		background: #e9ecef;
		border-radius: 8px;
		padding: 3px;
		overflow: auto
	}

	.module-tab-items::-webkit-scrollbar {
		display: none
	}

	.module-tab-item {
		cursor: pointer;
		position: relative
	}

	.module-tab-item:hover {
		color: #e50914
	}

	.module-tab-item.active {
		border-radius: 6px;
		background: #fff;
		font-weight: 700;
		color: #e50914
	}

	.module-class {
		padding: 5px 0
	}

	.module-class::after {
		border-color: #e9ecef;
		border-width: 2px 0
	}

	.module-info-poster {
		width: 220px;
		float: right;
		margin: 0 25px;
		position: relative;
		z-index: 10
	}

	.module-info-heading {
		margin: 0 0 20px 25px;
		position: relative
	}

	.view .module-info-heading::after {
		content: '';
		position: absolute;
		left: -25px;
		height: 100%;
		border-left: 2px dashed #dbdee2;
		bottom: -20px;
		z-index: -1
	}

	.module-info-heading h1::before {
		content: '';
		position: absolute;
		left: -25px;
		height: 40px;
		width: 5px;
		background: #e50914;
		border-radius: 0 4px 4px 0
	}

	.module-info-tag {
		display: flex;
		align-items: center
	}

	.module-info-content {
		position: relative
	}

	.module-info-content::after {
		content: '';
		width: 100%;
		height: 100%;
		position: absolute;
		left: 0;
		top: 0;
		border-radius: 18px;
		z-index: 9;
		box-shadow: 0 2.75px 2.21px rgba(0, 0, 0, 0.01), 0 6.65px 5.32px rgba(0, 0, 0, 0.02), 0 12.5px 10px rgba(0, 0, 0, 0.01), 0 22px 18px rgba(0, 0, 0, 0.03), 0 42px 33.4px rgba(0, 0, 0, 0.02), 0 100px 80px rgba(0, 0, 0, 0.01), 0 0 1px rgba(0, 0, 0, 0.1)
	}

	.module-info-content {
		border-radius: 0 18px 18px 18px;
		background: #fff
	}

	.module-info-items {
		min-height: 235px
	}

	.module-mobile-play,
	.module-poster-bg {
		display: none
	}

	.module-info-footer {
		background: #fafafa;
		z-index: 10;
		padding: 25px;
		border-radius: 0 0 18px 18px
	}

	.module-info-play {
		position: relative;
		z-index: 9;
		margin-top: -45px
	}

	.module-info-item:last-child {
		padding-bottom: 15px
	}

	.module-list {
		border-radius: 8px 0 8px 8px;
		padding: 20px
	}

	.player-heading .module-tab-items {
		padding: 0;
		border-radius: 8px 8px 0 0
	}

	.player-heading .module-tab-items-box {
		padding: 5px 5px 0
	}

	.player-heading .module-tab-item {
		line-height: 45px
	}

	.player-heading .module-tab-item.active {
		border-radius: 6px 6px 0 0
	}

	.module-player {
		margin-top: -30px
	}

	.app-downlist .app-downlist-go {
		line-height: 50px;
		font-size: 20px;
		margin-right: 20px
	}

	.app-downlist .app-downlist-go:last-child {
		margin: 0
	}

	.app-downlist .app-downlist-go::after {
		height: 55px
	}

	.app-downlist .app-downlist-go i {
		height: 20px;
		width: 20px;
		font-size: 20px;
		vertical-align: -1px
	}
}

@media (min-width: 559px) and (max-width: 1024px) {
	.logo {
		margin: 0;
		padding: 0;
		justify-content: center;
		width: 100px;
		left: -125px
	}

	.logo a {
		height: 32px;
		width: 32px;
		overflow: hidden
	}

	.sidebar {
		width: 100px;
		padding-top: 64px
	}

	.navbar-item {
		text-align: center;
		height: 78px;
		line-height: inherit;
		font-size: 14px
	}

	.navbar-item a {
		padding: 12px 0
	}

	.navbar-item.active {
		margin: 0 -1px 0 16px;
		padding: 0 15px 0 0;
		background: linear-gradient(to right, #fff 0%, #e9ecef 80%, #f7f8f9 80%)
	}

	.navbar-item i.icon-arrow-go {
		display: none
	}

	.navbar-item i {
		width: 100%;
		margin: 5px 0 2px;
		height: auto;
		font-size: 20px
	}

	.navbar-item span {
		margin: 0
	}

	.navbar-item.active .links::before,
	.navbar-item.active .links::after {
		bottom: -20px;
		right: -15px
	}

	.navbar-hr {
		margin: 5px 0
	}

	.main,
	.footer {
		margin-left: 100px;
		padding-left: 25px;
		padding-right: 25px
	}

	.footer-content {
		margin: 0 -25px;
		padding-right: 25px;
		padding-left: 25px
	}

	.header {
		margin: 0 0 0 100px;
		width: calc(100% - 100px)
	}

	.header .header-box {
		margin: 0 25px
	}

	.player-heading {
		margin-bottom: 15px
	}

	.player-heading .module-tab {
		margin: -3px 0 -15px
	}

	.module-player-info .module-info-heading {
		z-index: 2
	}

	.app-pic {
		width: calc(100% + 50px);
		margin: 60px -25px 0
	}
}

@media (max-width: 559px) {
	.nonenav {
		display: none;
	}

	.homepage:after {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		background: linear-gradient(to bottom, #f1f3f5 0%, #e3e6ea 100%);
		height: 175px
	}

	.display {
		display: none;
	}

	.drop:hover .shortcuts-mobile-overlay {
		background-color: rgba(0, 0, 0, 0.18)
	}

	.popup {
		width: 82%;
		padding: 0 25px
	}

	.popup-main p {
		font-size: 13px
	}

	.homepage .main {
		min-height: calc(100vh - 323px)
	}

	.page .main {
		padding-top: 54px;
		min-height: calc(100vh - 196px)
	}

	.page .header {
		display: none
	}

	.list .sidebar,
	.app .sidebar {
		background: #e9ecef
	}

	.page.open .header {
		display: inline-block
	}

	.open .header {
		z-index: 11
	}

	.header {
		position: relative;
		padding: 100px 15px 20px;
		height: 220px;
		width: 100%
	}

	.header-box {
		height: auto;
		justify-content: center
	}

	.header-op {
		display: none
	}

	.header-op-search {
		display: inline-block
	}

	.page .side-op {
		right: 0
	}

	.logo {
		position: initial;
		width: auto;
		padding: 0
	}

	.logo a {
		height: 32px
	}

	.sidebar {
		width: 100%;
		top: 0;
		display: flex;
		padding: 5px 0
	}

	.homepage .navbar {
		width: calc(100vw - 45px);
		transition: width .3s ease
	}

	.navbar.open,
	.page .navbar {
		width: calc(100vw - 85px)
	}

	.side-op {
		position: absolute;
		right: -40px;
		width: 85px;
		transition: right .3s ease;
		display: inline-block;
		font-size: 0;
		padding: 2px 10px 2px 5px;
		box-shadow: -15px 0 10px -12px rgba(0, 0, 0, 0.2)
	}

	.side-op.open {
		right: 0
	}

	.searchbar {
		background: #fff;
		box-shadow: 0 7px 21px rgba(149, 157, 165, 0.22), 0 0 1px rgba(0, 0, 0, 0.1);
		height: 50px
	}

	.search-box {
		position: absolute;
		top: 50px;
		width: 100%
	}

	.searchbar-main.open {
		position: fixed;
		height: 100vh;
		left: 0;
		top: 0;
		max-width: initial;
		background: linear-gradient(to bottom, #f7f8f9 0%, #fff 100%)
	}

	.searchbar-main.open::after {
		content: '';
		position: absolute;
		height: 100%;
		width: 100%;
		background: url(../images/colour-bg.png) no-repeat;
		background-size: 100%;
		top: 0;
		z-index: -1
	}

	.searchbar-main.open .searchbar {
		margin: 15px 55px 0 15px
	}

	.searchbar-main.open .search-recommend {
		width: 100%;
		left: 0;
		z-index: 9;
		padding: 20px 15px;
		background: transparent
	}

	.searchbar-main.open .cancel-btn {
		display: inline-block;
		margin: 0 15px 0 0;
		background: none;
		pointer-events: auto;
		position: absolute;
		right: -56px;
		font-weight: 700
	}

	.search-tag a {
		line-height: 34px;
		font-weight: 700;
		border-radius: 50px
	}

	.sidebar-bg {
		background: rgba(227, 230, 234, 0.98);
		z-index: 10
	}

	.navbar {
		overflow: auto;
		-webkit-overflow-scrolling: touch
	}

	.navbar::-webkit-scrollbar {
		display: none
	}

	.navbar-items {
		display: inline-flex;
		padding: 0 5px
	}

	.navbar-item {
		display: inline-block;
		white-space: nowrap
	}

	.navbar-item a {
		display: inline-block;
		font-size: 16px;
		font-weight: 700;
		color: #616161;
		padding: 0 10px 0;
		height: 44px;
		line-height: 44px
	}

	.navbar-item.active a {
		color: #0c0d0f
	}

	.navbar-item i {
		display: none
	}

	.navbar-item .current {
		position: absolute;
		background: #fcd877;
		width: 60%;
		height: 20%;
		bottom: 10px;
		left: 20%;
		z-index: -1
	}

	.navbar-hr {
		display: none
	}

	.radian::before,
	.navbar-item.active::before,
	.radian::after,
	.navbar-item.active::after,
	.radian .links::before,
	.navbar-item.active .links::before,
	.radian .links::after,
	.navbar-item.active .links::after {
		height: 0;
		width: 0
	}

	.main {
		padding: 20px 15px 0;
		position: relative
	}

	.page-heading {
		display: none
	}

	.module-heading {
		margin-bottom: 15px
	}

	.module-heading-tab {
		padding-top: 20px;
		margin: 0 -15px 15px;
		background: #e9ecef
	}

	.module-heading-tab::after {
		background: #e3e6ea
	}

	.module-heading-tab .module-heading-tab-link.active::after {
		bottom: -103%
	}

	.pianku .module-heading-tab {
		margin-bottom: -1px
	}

	.module-heading-search {
		border-bottom-width: 1px;
		padding: 25px 0 10px
	}

	.module-heading-tab .line {
		height: 18px;
		width: 3px
	}

	.module-heading-tab-link {
		font-size: 18px
	}

	.module-title {
		font-size: 20px
	}

	.module-title-en,
	.module-ranking-tab-info {
		opacity: .12;
		font-size: 24px;
		transform: scale(1, 1.1);
		bottom: 5px
	}

	.module-heading-more {
		font-size: 13px
	}

	.module-heading-more::after {
		border-color: #c2c6d0
	}

	.module-heading-more i {
		vertical-align: 0px;
		margin: 0 -2px 0 2px
	}

	.module-items {
		margin-right: -10px
	}

	.module-items.scroll-content .el-skeleton {
		width: calc(100vw - 30px)
	}

	.module-poster-item {
		margin: 0 10px 10px 0
	}

	.module-poster-item {
		width: calc(33.33% - 10px)
	}

	.module-poster-items-small .module-poster-item {
		width: 40vw;
		box-shadow: 0px 0 10px 0 rgba(0, 0, 0, 0.1);
		background: #fff;
		z-index: 1;
		border-radius: 8px
	}

	.module-poster-item-info {
		margin: 8px 0 4px
	}

	.module-paper-item-main a {
		min-height: 40px;
		padding: 8px 0;
		margin: 0 10px
	}

	.module-paper-item-main a:hover {
		padding: 8px 10px
	}

	.module-paper-item-main .icon-arrow-right {
		right: 8px
	}

	.module-paper-item-main a:hover .icon-arrow-right {
		right: 18px
	}

	.module-paper-item-main a:nth-last-child(-n+5) {
		display: none
	}

	.module-paper-item-num {
		font-size: 18px;
		width: 38px
	}

	.module-poster-items-small .module-item-nopic .module-item-pic::before {
		border: none
	}

	/*.module-poster-items-base .module-poster-item:nth-last-child(-n+4) {*/
	/*	display: none*/
	/*}*/
	.module-poster-items-base .module-poster-item:nth-child(n+13) {
		display: none
	}

	.module-poster-items-aline .module-poster-item:nth-last-child(-n+2) {
		display: inline-block
	}

	.module-poster-items-aline .module-poster-item:nth-last-child(-n+1) {
		display: none
	}

	.view .module-poster-items-base .module-poster-item:nth-child(16),
	.player .module-poster-items-base .module-poster-item:nth-child(16) {
		display: none !important
	}

	.module-paper-item:nth-last-child(-n+2) {
		display: inline-block
	}

	.module-poster-items-small .module-item-cover {
		padding-top: 55%;
		border-radius: 8px 8px 0 0
	}

	.module-poster-items-small .module-poster-item-info {
		padding: 0 10px;
		margin: 6px 0 7px
	}

	.module-card-item {
		width: calc(100% - 10px);
		box-shadow: 0px 15px 40px rgba(0, 0, 0, 0.05)
	}

	.module-card-item-poster {
		width: 110px;
		margin: -40px 0 0 -20px
	}

	.module-card-item:after {
		border: none
	}

	.module-card-item-info {
		margin-left: 105px;
		min-height: 95px
	}

	.module-card-item-class,
	.module-card-item-info .module-info-item {
		font-size: 12px
	}

	.module-card-item-footer a {
		margin: 0 -5px 0 15px
	}

	.module-tab-items {
		display: none
	}

	.module-tab-item {
		width: calc(33.33% - 10px);
		margin: 5px;
		border-radius: 8px;
		background: #f1f3f5;
		color: #424242;
		padding: 0 15px;
		position: relative;
		text-align: center;
		line-height: 40px;
		font-size: 14px
	}

	.module-tab-item.active {
		color: #e50914;
		background: #ffeae5;
		font-weight: 700;
		box-shadow: none
	}

	.module-tab-item span {
		width: calc(100% + 30px);
		height: 100%;
		display: inline-block;
		margin: 0 -15px
	}

	.module-tab-items-box {
		margin: 0 -5px;
		padding-bottom: 15px
	}

	.module-tab-drop {
		position: relative
	}

	.speed {
		left: 10px;
		top: 5px
	}

	.module-tab .module-tab-items {
		position: fixed;
		width: 100%;
		padding: 15px 25px 0;
		min-height: 50vh;
		max-height: 80vh;
		border-radius: 18px 18px 0 0;
		display: inline-block;
		left: 0;
		bottom: 0;
		z-index: 5000;
		background: #fff url(../images/colour-bg.png) no-repeat;
		background-size: 150%;
		background-position-y: -20px;
		transform: translate3d(0, 100%, 0);
		visibility: hidden;
		transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1), visibility 0.25s linear 0s
	}

	.module-tab.module-tab-drop .module-tab-items {
		transform: translateZ(0)
	}

	.module-tab-drop+.shortcuts-mobile-overlay {
		top: 0;
		background-color: rgba(0, 0, 0, 0.6);
		pointer-events: auto
	}

	.module-tab-title {
		position: relative;
		display: block;
		text-align: center;
		font-size: 18px;
		font-weight: 700;
		padding: 10px 5px 15px;
		margin-bottom: 10px
	}

	.module-tab-name {
		font-size: 13px;
		display: inline-block !important;
		padding: 0 12px;
		background: #e9ecef
	}

	.module-tab-name i {
		font-size: 10px;
		transform: scale(0.6);
		margin: -10px 0 0 3px;
		font-weight: 900;
		vertical-align: 0px
	}

	.close-drop {
		position: absolute;
		left: 0;
		top: 9px;
		height: 30px;
		width: 30px;
		line-height: 28px;
		border-radius: 50px
	}

	.close-drop:hover {
		background: #fff0ec;
		color: #e50914
	}

	.close-drop i {
		font-size: 12px;
		transform: scale(0.8);
		font-weight: 700;
		vertical-align: 0px
	}

	.module-page {
		margin-top: 15px
	}

	.module-class {
		margin: 0 -15px;
		background: #fff;
		padding: 8px 15px
	}

	.module-class-items {
		padding: 0 15px !important
	}

	.module-class-item {
		padding: 2px 0
	}

	.module-item-title {
		margin-right: 12px;
		width: 40px;
		line-height: 40px;
		font-size: 14px
	}

	.module-item-title i {
		margin-left: 4px
	}

	.module-item-box {
		display: inline-flex
	}

	.module-item-box a {
		padding: 5px 10px;
		color: #424242;
		font-size: 13px;
		margin-right: 0
	}

	.module-item-box a::after {
		border: 0
	}

	.module-footer {
		border: none;
		margin: 0
	}

	.module-list {
		padding: 15px;
		border-radius: 8px
	}

	.module-play-list-link::after {
		border-color: #dbdee2
	}

	.module-play-list-base .module-play-list-link {
		width: calc(25% - 10px)
	}

	.module-play-list-large .module-play-list-link {
		width: calc(50% - 10px)
	}

	.module-play-list-larger .module-play-list-link {
		min-width: calc(50% - 10px)
	}

	.module-mobile-play {
		text-align: center;
		padding: 30px 0 10px
	}

	.module-info .module-main {
		position: relative;
		z-index: 9;
		border-radius: 18px 18px 8px 8px;
		box-shadow: 0 2.75px 2.21px rgba(0, 0, 0, 0.01), 0 6.65px 5.32px rgba(0, 0, 0, 0.02), 0 12.5px 10px rgba(0, 0, 0, 0.01), 0 22px 18px rgba(0, 0, 0, 0.03), 0 42px 33.4px rgba(0, 0, 0, 0.02), 0 100px 80px rgba(0, 0, 0, 0.01), 0 0 1px rgba(0, 0, 0, 0.1);
		background: #fff url(../images/colour-bg.png) no-repeat;
		background-size: 150%;
		background-position-y: -20px;
		margin-top: 210px
	}

	.module-poster-bg {
		width: 100%;
		position: absolute;
		top: -170px;
		left: 0;
		overflow: hidden;
		border-radius: 0
	}

	.module-poster-bg::before {
		content: '';
		height: 100%;
		width: 100%;
		bottom: 0;
		border-radius: 0;
		position: absolute;
		z-index: 2;
		background: rgba(255, 255, 255, 0.68);
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px)
	}

	.module-poster-bg:after {
		content: '';
		position: absolute;
		z-index: 2;
		opacity: .1;
		height: 100%;
		width: 100%;
		top: 0;
		background-image: url(../images/frosted.png);
		background-size: 30%;
		filter: invert(100%)
	}

	.module-poster-bg .module-item-cover:after {
		content: '';
		position: absolute;
		z-index: 2;
		opacity: .26;
		height: 100%;
		width: 100%;
		top: 0;
		background-image: url(../images/frosted.png);
		background-size: 29%
	}

	.module-poster-bg .module-item-cover:before {
		content: '';
		position: absolute;
		z-index: 2;
		height: 30%;
		width: 100%;
		bottom: 0;
		background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, #fff 100%)
	}

	.module-poster-bg .module-item-cover {
		border-radius: 0;
		width: 100%;
		left: 0%
	}

	.module-info-poster {
		position: relative;
		top: -180px;
		margin: 0 auto -160px !important;
		width: 170px !important
	}

	.module-info-poster .module-item-cover {
		box-shadow: 0 20px 15px -10px rgba(0, 0, 0, 0.2), 0 50px 35px -40px rgba(0, 0, 0, 0.4), 0 50px 35px -40px rgba(0, 0, 0, 0.2)
	}

	.module-info-poster .module-item-nopic .module-item-pic::before {
		border: none
	}

	.view .module-info-heading {
		text-align: center;
		padding: 5px 0 0
	}

	.view .module-info-heading h1 {
		padding: 0 20px
	}

	.module-info-items {
		padding: 20px
	}

	.module-info-introduction::after {
		background: #e3e6ea
	}

	.module-info-introduction-content {
		-webkit-line-clamp: 5;
		max-height: 111px
	}

	.module-info-tag-link {
		margin-right: 6px
	}

	.module-info-tag-link a {
		color: #757575
	}

	.module-info-footer {
		display: none
	}

	.module-info-item:last-child {
		padding-bottom: 0
	}

	.player-box,
	.module-player-info {
		margin: 0 -15px;
		border-radius: 0
	}

	.module-player-info::after {
		border-radius: 0;
		box-shadow: 0 1px 0 #f1f3f5
	}

	.module-player-info {
		padding: 15px 0 20px;
		margin-bottom: 30px
	}

	.module-player-info .module-info-heading {
		max-width: 72%;
		margin: 0 0 0 15px;
		position: relative;
		z-index: 9
	}

	.module-player-info .module-info-heading h1 {
		font-size: 20px;
		margin-bottom: 6px
	}

	.module-player-info .module-info-tag-link {
		font-size: 12px;
		height: 26px;
		line-height: 26px;
		padding: 0 10px
	}

	.module-player-handle-items {
		top: 12px;
		right: 0
	}

	.module-player-handle-item {
		margin-left: 0
	}

	.handle-btn {
		height: 35px;
		width: 40px;
		background: transparent
	}

	.handle-btn-name {
		display: none
	}

	.handle-btn-icon {
		font-size: 16px
	}

	.handle-btn-icon i {
		vertical-align: -1px
	}

	.module-jump-prompt {
		width: 90vw;
		left: 5vw;
		top: 4vh;
		margin: 0;
		height: 70vh
	}

	.player-rm {
		padding: 0;
		z-index: 9
	}

	.rm-two a {
		margin: 5px;
		width: calc(100% - 10px)
	}

	.rm-two a:last-child {
		margin-top: 0
	}

	.app-module::after {
		background-size: 140%;
		background-position-x: -130px
	}

	.app-info {
		text-align: center;
		padding: 0 20px
	}

	.app-title {
		font-size: 30px
	}

	.app-desc {
		font-size: 14px
	}

	.app-downlist {
		text-align: center
	}

	.app-downlist .app-downlist-go {
		margin: 10px 5px !important;
		width: 130px;
		padding: 0 15px
	}

	.app-downlist .app-downlist-go i {
		font-size: 14px;
		vertical-align: -1px
	}

	.app-pic {
		width: calc(100% + 30px);
		margin: 40px -15px 0;
		height: 350px
	}

	.app-pic::before,
	.app-pic::after {
		width: calc(50% - 80px);
		background: rgba(247, 248, 249, 0.8)
	}

	.app-pic-list {
		-webkit-animation: move 180s linear infinite;
		width: 600%
	}

	@-webkit-keyframes move {
		0% {
			transform: translateX(0px)
		}

		100% {
			transform: translateX(-100%)
		}
	}

	.skeleton.module-play-list-link {
		height: 30px
	}

	.module-play-list-larger .skeleton.module-play-list-link {
		height: 36px
	}

	.module-shadow .scroll-box {
		margin-bottom: -80px;
		padding-bottom: 100px
	}

	.scroll-box {
		margin: 0 -15px;
		padding: 0 15px 15px;
		overflow-y: auto;
		webkit-overflow-scrolling: touch
	}

	.scroll-content {
		display: inline-flex
	}

	.scroll-content .module-paper-item {
		width: 68vw;
		margin: 1px 15px 0 0
	}

	.scroll-box::-webkit-scrollbar {
		display: none
	}

	.footer .content {
		margin: 0 15px;
		width: auto
	}

	.footer-content {
		margin: 0 -15px;
		padding: 20px 0
	}

	.foot-copyright a:first-child {
		margin: 0
	}

	.links-list .module-main {
		margin: 0 -5px
	}

	.links-list-go {
		width: calc(50% - 10px);
		margin: 5px;
		display: inline-block;
		color: #616161;
		line-height: 34px;
		padding: 0 12px;
		border-radius: 8px;
		background: #fff
	}

	.m-module-tab-week {
		width: 100%;
		display: flex;
		justify-content: space-between;
		background: none !important;
	}

	.m-module-heading {
		display: flex;
		flex-direction: column;
		align-items: baseline;
	}

	.m-module-tab {
		width: 100%;
	}

	.m-module-tab-week {
		width: 100%;
		display: flex;
		justify-content: space-between;
		background: none !important;
	}

	.m-tab-item.active {
		background: none !important;
	}

	.m-tab-item {
		width: auto;
		padding: 0;
		margin: 0;
		background: none !important;
	}
}

.module-tab-item small {
	background: linear-gradient(90deg, #fafafa, #fff0ec);
	color: #757575;
	box-shadow: 0px 3px 10px rgba(0, 0, 0, 0.2);
	position: absolute;
	right: -3px;
	top: -3px;
	transform: scale(0.8);
	transform-origin: top right;
	font-weight: normal;
	padding: 0 6px;
	border-radius: 8px;
	height: 19px;
	line-height: 18px;
	font-size: 12px
}

.module-tab-item small.no {
	right: 0;
	top: -1px;
	height: 16px;
	line-height: 15px;
	padding: 0 4px;
}

.module-tab-item.active small {
	background: #e50914;
	color: #fff;
	background: linear-gradient(to right, #ff711f 0%, #e50914 100%);
	color: #fff
}

.container-slide {
	position: relative;
}

.swiper {
	width: 100%;
	overflow: hidden;
}

.swiper-big .swiper-slide .banner {
	position: relative;
	display: block;
	height: 28vw;
	min-height: 180px;
	max-height: 520px;
	border-radius: 8px;
	overflow: hidden;
}

.swiper-big .swiper-slide .banner::before {
	content: "";
	position: absolute;
	left: 0;
	top: 0;
	z-index: 1;
	width: 100%;
	height: 100%;
	background-color: rgba(1, 1, 1, .2);
	pointer-events: none;
}

.swiper-big {
	min-height: 180px;
	max-height: 520px;
	margin: 0 0 20px;
	border-radius: 8px;
}

.swiper-big img {
	width: 100%;
	height: 370px;
	object-fit: cover;
}

.sm-swiper {
	position: absolute;
	right: 100px;
	top: 50%;
	z-index: 9;
	width: 220px;
	transform: translateY(-50%);
}

.swiper-small {
	width: 85%;
	margin: 0 auto;
}

.swiper-small .swiper-slide .pic {
	width: 126px;
	height: 168px;
	margin: 0 auto;
	background-color: #373737;
	border-radius: 5px;
	overflow: hidden;
}

.swiper-small .swiper-slide .pic img {
	width: 100%;
	object-fit: cover;
}

.swiper-small .swiper-slide {
	display: block;
	padding-left: 10px;
	width: 100%;
	height: 100%;
}

.swiper-small .swiper-slide {
	color: #fff;
}

.swiper-small .swiper-slide .title a {
	display: block;
	width: 100%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	font-size: 24px;
	color: #ffffff;
	padding: 15px 0;
}

.swiper-small .swiper-slide .ins p {
	color: #ffffff;
	font-size: 14px;
	opacity: .7;
	padding: 3px 0;
	margin: 0;
}

.swiper-small .swiper-slide .ins {
	display: -webkit-box;
	overflow: hidden;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.swiper-button-next::after,
.swiper-button-prev::after {
	content: "" !important;
}

.swiper-button-next,
.swiper-button-prev {
	-webkit-tap-highlight-color: transparent !important;
	-webkit-user-drag: none !important;
	-webkit-user-select: none !important;
	-moz-user-select: none !important;
	-ms-user-select: none !important;
	user-select: none !important;
	width: 20px !important;
	height: 20px !important;
	outline: none !important;
}

.swiper-button-next {
	right: -10px !important;
}

.swiper-button-prev {
	left: -10px !important;
}

.swiper-pagination {
	width: 90% !important;
	left: 50% !important;
	transform: translateX(-50%) !important;
}

.swiper-pagination span {
	margin: 0 7px;
}

.swiper-pagination-bullet-active {
	transform: scaleX(2.66) !important;
	background-color: #e50914 !important;
}

.mobile-v-info {
	display: none;
}

.swiper-pagination-bullet {
	width: 6px !important;
	height: 6px !important;
	border-radius: 0 !important;
	cursor: pointer !important;
}

@media screen and (max-width: 720px) {
	.swiper-big .swiper-slide .banner {
		padding-top: 50%;
	}
}

@media screen and (max-width: 1024px) {
	.sm-swiper {
		display: none;
	}

	.mobile-v-info {
		position: absolute;
		left: 0;
		bottom: 0px;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		width: 100%;
		height: 100%;
		z-index: 9;
		padding: 10px;
		background-color: rgba(1, 1, 1, .2);
		color: #fff;
		pointer-events: none;
	}

	.mobile-v-info .v-title {
		padding: 5px 0;
		font-size: 24px;
	}

	.mobile-v-info .v-ins p {
		display: -webkit-box;
		overflow: hidden;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		width: 50%;
		font-size: 14px;
		opacity: .7;
		margin: 0 0 5px;
	}
}

@media screen and (max-width: 1300px) {
	.swiper-pagination {
		display: none;
	}
}

@media screen and (max-width: 1200px) {

	.sm-swiper {
		display: none;
	}

	.mobile-v-info {
		position: absolute;
		left: 0;
		bottom: 0px;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		width: 100%;
		height: 100%;
		z-index: 9;
		padding: 10px;
		background-color: rgba(1, 1, 1, .2);
		color: #fff;
		pointer-events: none;
	}

	.mobile-v-info .v-title {
		padding: 5px 0;
		font-size: 24px;
	}

	.mobile-v-info .v-ins p {
		display: -webkit-box;
		overflow: hidden;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		width: 50%;
		font-size: 14px;
		opacity: .7;
		margin: 0 0 5px;
	}
}

#report-popup .popup-main {
	padding-bottom: 30px;
	min-height: 291px
}

.popup-main p {
	padding: 12px 0 0
}

.report-content {
	width: 100%;
	min-height: 100px;
	padding: 15px;
	margin: 10px 0 5px;
	resize: auto;
	background: #eaedf1;
	border-radius: 10px
}

.popup strong {
	color: #ff2a14
}

.report-input,
.report-verify {
	padding: 0;
	width: 45%;
	min-height: auto;
	height: 35px;
	line-height: 35px;
	float: left;
	text-align: center;
	border-radius: 10px;
	margin: 0;
}

.report-verify {
	float: right;
}

.verify-box {
	overflow: hidden;
	margin-bottom: 15px;
}

img.report-verify {
	vertical-align: middle;
	height: 35px !important;
	width: 45% !important;
}

.gbook_submit.popup-btn {
	width: 100%;
}

.close-popup {
	position: absolute;
	right: calc(8% - 20px);
	border-radius: 50px;
	top: 10px;
	width: 34px;
	line-height: 34px;
	height: 32px;
	text-align: center;
	cursor: pointer;
	box-shadow: 0 7px 21px rgb(149 157 165 / 22%);
	background: #fff
}

.close-popup i {
	transform: scale(.88);
	font-size: 12px;
	color: #282828;
	margin-right: 0;
}

.close-popup:hover i {
	color: #ff2a14
}

.message .msg-content .msg-send {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin: 24px 0
}

.message .msg-content .msg-send .check span {
	display: inline-block;
	padding: 10px;
	background-color: #ECECEC
}

.mac_total {
	margin-right: 0 !important
}

.message .msg-content .msg-wrap .msg-item .msg-item-left .content {
	color: #282828;
	word-break: break-word;
	margin: 0 0 5px;
	line-break: anywhere;
	text-align: justify;
}

.msg-item,
.msg-item-left,
.info {
	color: rgba(0, 0, 0, 0.6)
}

.msg-item-right {
	position: absolute;
	right: 0;
	top: 0
}

.msg-item {
	position: relative
}

.message .msg-content textarea {
	width: 100%;
	resize: none;
	background-color: #F2F2F2;
	outline: none;
	border: none;
	padding: 20px;
	border-radius: 8px;
}

.message .msg-content .msg-wrap .msg-all {
	padding-bottom: 0 !important;
}

.message .msg-content .msg-send .btn button {
	display: inline-block;
	padding: 6px 26px;
	color: #fff;
	outline: none;
	border: none;
	border-radius: 5px;
	background-color: #e50914;
	cursor: pointer;
}

.message .msg-content .msg-wrap .msg-item {
	display: flex;
	justify-content: space-between;
	margin-top: 24px;
}

.message .msg-content .msg-wrap .msg-item .msg-item-left .uname {
	color: #333;
	margin-bottom: 5px;
	font-size: 16px;
	font-weight: 700;
}

.message .module-title {
	padding-bottom: 20px;
}

.form-control.verify {
	width: 90px;
	text-align: center;
	margin-right: 10px;
	display: inline-block;
	background-color: #F5F5F5;
	color: #999999;
	border: 1px solid #eee;
	border-radius: 5px;
	height: 35px;
	padding: 0 10px;
	font-size: 12px;
	line-height: 20px;
}

img#verify_img {
	width: 98px;
	margin-right: 10px;
	border: 0;
	vertical-align: middle;
	height: 35px;
}

.text-muted {
	font-size: 12px;
}

.message .msg-content .msg-wrap .msg-item {
	border-bottom: 1px solid #eee;
	padding-bottom: 10px;
}

.glyreply {
	color: red;
	display: block;
	margin: 0 0 5px;
}

.gg-icon {
	position: absolute;
	left: 5px;
	width: 38px;
	height: 22px;
	margin-right: 3px;
	z-index: 8;
	vertical-align: middle;
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEwAAAAsCAMAAAD4va5DAAAAolBMVEX///8AAAAAAAAAAAAAAAACAgIAAAAAAAAGBgb5+fns7OwAAACBgYHq6ur9/f17e3tgYGD09PSoqKiLi4sAAAANDQ3V1dW6urqPj48DAwMAAAD////u7u7j4+MbGxv////b29vBwcH19fXt7e3GxsaLi4ukpKRTU1P4+Pjl5eXh4eHX19fLy8vb29v4+PisrKwvLy86Ojr7+/vy8vK4uLhVVVX0WqNFAAAANnRSTlOZAgULFR0IACCBYxIvW4sxJmk0HxoXU0grJQ6TZl0cj1RFcmtENTUmfGFYTUlGcT4rI4dyMhsJ24tXAAABiElEQVRIx+3XyW6DMBCA4WnAzrCUJUDYCpQAIWuTdHn/V6sNqZqFAwSfqv4HJHP4ZECIAabT6ZOQGARnazKqhuBYI1EqjYjSxpsCt6gUaZr8cJoWSZRr0FiObcCIDNvhWoNJTgYjyxypxWhkw+jsiHJsQjVjPGZodMIxSQMBaVKLySIw+R/jxX5lXK2LFYDqK4/tLCeby+UadwA7XAzECj/Py7KcuXVZ5rnv+zHfmEXYzvbuUOwVyU+IGIYhxxLipp4ne15K2Dm/P0Y2ylV8tyQNWF+yEwS6ac4HYC9w0wLT5vr28tvgy7zD3msbhGHqRm369LZqW/Y4FiJxWR7LdQnBMFR6Ylss4KYkSZ4vYkujJ7bDhbjX6YAruGu9nLGW7THvj1Vo3GNzcrIsq64tFtH7YyeELuz3qaT9MQUrcZiKW3HYISw6MSvQddM0dT0gvbH4eIy7MLS41Who9r9nc+hoXX3Auazy/84HZSAmdjwQOriIHKmEDnuCx1ChA7LI0V3sT4XQ351vRIIkxg4KQCAAAAAASUVORK5CYII=);
	background-size: cover;
}

.module-adslist {
	border-radius: 5px;
	margin-top: 10px;
}

.ads_w a {
	width: 100%;
	display: block;
	padding: 3px;
	position: relative;
}

.ads_w img {
	border-radius: 4px;
}

.copyright {
	background: #000;
	overflow: hidden;
	position: relative
}

.copyright .vague {
	object-fit: cover;
	display: block;
	width: 100%;
	height: 100%;
	overflow: hidden;
	border-radius: 6px;
	-webkit-transition: all .25s ease-in-out;
	transition: all .25s ease-in-out;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	filter: blur(60px);
	-webkit-filter: blur(60px);
	-moz-filter: blur(60px);
	-ms-filter: blur(60px);
	-o-filter: blur(60px)
}

.copyright .txt {
	color: #fff;
	font-size: 20px;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 99%;
	text-align: center
}

.module-play-list::-webkit-scrollbar-track,
.v3-app-layout__side__Short::-webkit-scrollbar-track,
.module-play-list::-webkit-scrollbar-track,
.v3-app-layout__side__Short::-webkit-scrollbar-track {
	background: transparent;
}

.module-play-list::-webkit-scrollbar,
.v3-app-layout__side__Short::-webkit-scrollbar,
.player .module-list::-webkit-scrollbar,
.v3-app-layout__side__Short::-webkit-scrollbar {
	width: 5px;
}

.module-play-list::-webkit-scrollbar-thumb,
.v3-app-layout__side__Short::-webkit-scrollbar-thumb,
.player .module-list::-webkit-scrollbar-thumb,
.v3-app-layout__side__Short::-webkit-scrollbar-thumb {
	border-radius: 5px;
	height: 10px;
	background: #C1C1C1;
}

.player .module-list::-webkit-scrollbar-track,
.v3-app-layout__side__Short::-webkit-scrollbar-track {
	background: #e9ecef;
}

.module-tab-week {
	background: #e9ecef;
	border-radius: 8px;
	padding: 3px;
	overflow: auto
}

.module-item-new {
	position: absolute;
	top: 0;
	right: 0;
	color: #fff;
	border-radius: 0 5px 0 5px;
	padding: 0px 5px;
	background: #e50914;
	z-index: 1;
	font-size: 12px;
}

.header-op-user {
	padding: 0 16px;
	height: 36px;
	font-size: 14px;
	background: #fe3355;
	position: relative;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	min-width: 88px;
	border-radius: 42px;
	color: #fff;
	margin-left: 10px;
	cursor: pointer;
}

.header-op-user:hover {
	background: #EA2F4E;
}

.header-op-list {
	display: flex;
	align-items: center;
	font-size: 14px;
}

.member_group {
	position: relative;
	margin-left: 10px;
}

.member_group a {
	display: block;

}

.member_group:hover .user_list_drop,
.user_list_drop:hover {
	display: block;
}

.drop_content {
	width: 132px;
	overflow: hidden;
	border-radius: 4px;
	padding: 8px;
	box-shadow: 0px 2px 4px rgb(0 0 0 / 5%), 0px 4px 16px rgb(0 0 0 / 10%);
	background: #fff;
}

.member_group .user_list_drop {
	position: absolute;
	top: 36px;
	right: 0;
	display: none;
	padding-top: 8px;
}

.member_group .user_list_drop ul {
	padding-bottom: 8px;
	border-bottom: 1px solid rgba(12, 13, 15, 0.06);
}

.member_group .user_list_drop .logout {
	padding-top: 8px;
}

.member_group .user_list_drop .logout a,
.member_group .user_list_drop li a {
	display: flex;
	align-items: center;
	width: fit-content;
	height: 40px;
	line-height: 40px;
	border-radius: 4px;
	margin: auto;
	padding: 0 12px;
}

.member_group .user_list_drop .logout a:hover,
.member_group .user_list_drop li a:hover {
	background-color: rgba(0, 0, 0, 0.04);
	color: #282828;
}

.member_group .user_list_drop .logout a .icon,
.member_group .user_list_drop li a .icon {
	margin-right: 12px;
	line-height: 18px;
}

.member_group .useimg {
	width: 36px;
	height: 36px;
	border-radius: 50%;
	cursor: pointer;
	vertical-align: middle;
}

/*登录弹窗样式*/

.input-list {
	margin-bottom: 2rem;
}

.input-list .mac_login_form li {
	padding: 10px 10px;
}

/*.input-list li input.form-control {height: 40px;}*/
.mac_login_form .user-verify {
	float: right;
	height: 40px !important;
	line-height: 40px;
	border-radius: 8px;
	margin-right: 0 !important;
	width: 40% !important;
}

.mac_login_form .form-control {
	width: 100%;
	font-size: 12px;
	line-height: 20px;
	transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
	background-color: #F5F5F5;
	border-radius: 5px;
	padding: 0 10px;
	color: #999;
	height: 40px;
	border-bottom: .0625rem solid #e2e2e2;
	border: 1px solid #eee;
}

.mac_login_form .login_form_submit {
	width: 100%
}

.input-list .mac_login_form a {
	width: 25%;
	float: left;
	text-align: center;
}

@media(max-width:320px) {
	.popup {
		width: 90%;
		padding: 0 10px
	}
}

.mac_login_form .form-control:focus {
	border-color: #ff9900;
}

.noplaylist {
	background: #d7dae1;
	color: #8f8f8f !important;
	border-radius: 50px;
	padding: 0 30px;
	position: relative;
	overflow: hidden;
	cursor: not-allowed;
}

.btn-collect {
	background-color: #fc000c;
	background: linear-gradient(to right, #fc000c 0, #f9444d 100%);
	cursor: pointer;
	border-radius: 50px;
	padding: 0 30px;
	color: #fff !important;
	position: relative;
	overflow: hidden;
	margin-left: 10px;
}

.links small {
	background: linear-gradient(to right, #ff711f 0%, #e50914 100%);
	color: #fff;
	box-shadow: 0px 3px 10px rgb(0 0 0 / 20%);
	position: absolute;
	right: 15px;
	transform: scale(0.8);
	transform-origin: top right;
	font-weight: normal;
	padding: 0 6px;
	border-radius: 8px;
	height: 19px;
	line-height: 18px;
	font-size: 12px;
}

@media (min-width: 559px) and (max-width: 1024px) {
	.links small {
		display: none;
	}
}

@media (max-width: 559px) {
	.links small {
		right: -10px !important;
	}
}

.nofound {
	font-size: 24px;
	font-weight: 700;
	margin-bottom: 5px
}

.mx-mac_msg_jump {
	box-shadow: 0 7px 21px rgba(149, 157, 165, .22);
	margin: 35px auto;
	padding: 50px 25px;
	width: 100%;
	border-radius: 4px
}

.mx-mac_msg_jump .msg_jump_tit {
	margin-bottom: 25px;
	text-align: center;
	line-height: 26px;
	color: #222;
	font-size: 1.5rem;
	font-weight: 700
}

.mx-mac_msg_jump .text {
	margin-bottom: 6px;
	color: #222;
	font-size: 14px
}

.mx-mac_msg_jump .title {
	margin-bottom: 6px;
	color: #666;
	font-size: 14px
}

@media (min-width:768px) {
	.mx-mac_msg_jump {
		margin: 35px auto;
		padding: 50px;
		width: 400px
	}
}

.mx-mac_msg_jump .form .item1 {
	position: relative;
	border: 1px solid;
	border-radius: 4px;
	line-height: 43px;
	border-color: #f2f2f2
}

.mx-mac_msg_jump .form .item1 input {
	display: inline-block;
	padding: 0 10px;
	width: 65%;
	border: none;
	background: 0 0;
	line-height: inherit
}

.mx-mac_msg_jump .form .item1 .get-pwd {
	float: right;
	display: inline-block;
	padding-right: 10px;
	width: 35%;
	text-align: right;
	line-height: inherit;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap
}

.btnpwd {
	display: inline-block;
	margin-top: 25px;
	width: 100%;
	border: none;
	border-radius: 4px;
	line-height: 45px;
	cursor: pointer;
	text-align: center;
	background: #ff2a14;
	color: #fff
}

.btnpwd:hover {
	background: linear-gradient(90deg, #ff9800, #ff2a14) !important;
	color: #fff
}

.btnlogin {
	display: inline-block;
	margin: 5px 10px 0;
	padding: 0 32px;
	border-radius: 32px;
	line-height: 36px;
	background: #ff2a14;
	color: #fff;
}

.btnlogin:hover {
	background: linear-gradient(90deg, #ff9800, #ff2a14) !important;
	color: #fff;
}

.btncz {
	display: inline-block;
	margin: 5px 10px 0;
	padding: 0 32px;
	border-radius: 32px;
	line-height: 36px;
	border: 1px solid #ff2a14;
	color: #ff2a14;
}

.item1 input:-webkit-autofill {
	-webkit-box-shadow: 0 0 0px 1000px white inset
}



.weui_msg_jump {
	padding-top: 20%;
	text-align: center;
}

.weui_icon {
	margin-bottom: 0.75rem;
}

.weui_icon i {
	font-size: 3.5rem;
	color: #F44336;
	line-height: 1em;
}

.weui_text {
	margin-bottom: 0.625rem;
	padding: 0 0.5rem;
}

.weui_msg_title {
	margin-bottom: 0.5rem;
	font-weight: 400;
	font-size: 2rem;
}

.weui_text p {
	line-height: 2.5rem;
}

.weui_text .weui_xtts {
	color: #333;
	font-size: 1.3rem;
}

.weui_text .weui_tips {
	color: #999;
	font-size: 0.325rem;
}

.weui_text .weui-p {
	padding: 0.25rem 0;
}

.weui_text .weui-a {
	background: none;
	border: 1px solid #ccc;
	color: #333;
}

.weui_text .weui_tzt a {
	color: #10AEFF
}

.bstyle {
	color: #fe3355
}