<!DOCTYPE html>
<html lang="zh-CN">
<head>
    {include file="public/include"}  
    <title>系统提示......</title>
</head>
<body>
    		<div class="page list">
  {include file="public/head"}   
	<div class="main">
   	<div class="content">   
<div class="mx-mac_msg_jump">
    <div class="msg_jump_tit">系统提示</div>
    <div class="title">亲爱的用户:</div>
    <div class="text">访问此数据需要密码</div>
    <div class="form">
        <form id="form1" name="form1" action=""  method="post">
            <div class="item1">
                <input type="password" name="pwd" placeholder="请在此输入访问密码..."/>
                   {if condition="$obj.vod_pwd_url neq ''"}
                    <a href="{$obj.vod_pwd_url}" target="_blank" class="get-pwd">获取密码</a>
                {else/}
                    <span class="get-pwd">密码：{$obj.vod_pwd}</span>
                {/if}
            </div>
            <div class="item">
                <a class="submit_btn btnpwd" href="javascript:;"  data-mid="1" data-id="{$obj.vod_id}" data-type="1">点击确认</a>
            </div>
        </form>
</div>
</div>
</div>
</div>
</div>
      {include file="public/foot"} <!-- 底部-->
      	<script type="text/javascript">
		$('body').on('click', '.submit_btn', function(e){
			var $that = $(this);
	       if($that.attr("data-id")){
	                 $.ajax({
                        url: maccms.path + '/index.php/ajax/pwd.html?id=' + $that.attr("data-id") + '&mid=' + $that.attr("data-mid") + '&type=' + $that.attr("data-type") + '&pwd='+ $that.parents('form').find('input[name="pwd"]').val(),
                        cache: false,
                        dataType: 'json',
                        success: function ($r) {
                            $that.addClass('disabled');
                            layer.msg($r.msg)
                            if ($r.code == 1) {
                                top.location.reload();
                            }
                        }
                    });
	       }
		});
	</script>
</body>
</html>



