<a href="{if condition="$mxprost['mxprocms']['s2']['tzzt'] eq 1"}{:mac_url_vod_play($vo)}{else}{:mac_url_vod_detail($vo)}{/if}" title="{$vo.vod_name}"  class="module-poster-item module-item {if condition="$key eq 1"}top top1{/if} {if condition="$key eq 2"}top top2{/if} {if condition="$key eq 3"}top top3{/if}">
<div class="module-item-cover">
<div class="module-item-top {if condition="$key eq 1"}top top1{/if} {if condition="$key eq 2"}top top2{/if} {if condition="$key eq 3"}top top3{/if}">{$key}</div>
<div class="module-item-note">{if condition="$vo.vod_remarks neq ''"}{$vo.vod_remarks}{elseif condition="$vo.vod_serial gt 0"}第{$vo.vod_serial}集{else /}已完结{/if}</div>
<div class="module-item-pic"><img class="lazy lazyload" data-original="{$vo.vod_pic|mac_url_img}" alt="{$vo.vod_name}"  referrerpolicy="no-referrer" src="{:mac_url_img($mxprost.mxprocms.s1.pic)}"></div>
</div>
<div class="module-poster-item-info">
<div class="module-poster-item-title">{$vo.vod_name}</div>
</div>
</a>