{if condition="$__PAGING__.page_total gt 1"}
      <div id="page">
        <a href="{$__PAGING__.page_url|mac_url_page=1}" class="page-link page-previous" title="首页">首页</a>
        <a href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_prev}" class="page-link page-previous" title="上一页">上一页</a>
        {maccms:foreach name="$__PAGING__.page_num" id="num"}
        {if condition="$__PAGING__['page_current'] eq $num"}
        <span class="page-link page-number page-current display">{$num}</span>
        {else}
          <a href="{$__PAGING__.page_url|mac_url_page=$num}" class="page-link page-number display" title="第{$num}页">{$num}</a>
            {/if}   
         {/maccms:foreach}
           <a href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_next}" class="page-link page-next" title="下一页">下一页</a>
        <a href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_total}" class="page-link page-next" title="尾页">尾页</a>
      </div>
{/if}
