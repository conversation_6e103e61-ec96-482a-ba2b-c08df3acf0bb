<!DOCTYPE html>
<html>
 <head> 
   	{include file="public/include"}
  <title>提现记录 - 个人中心 - {$maccms.site_name}</title> 
  <meta name="keywords" content="个人中心,{$maccms.site_keywords}" /> 
  <meta name="description" content="{$maccms.site_description}" />
  {include file="user/include"}   
 </head>
 <body class="mxui-min-width">
  <div class="page list">
      {include file="public/head"} 
   <div class="mxui-part-case main"> 
    <div class="mxui-user-head mxui-margin-top mxui-back-whits"> 
     <div class="mxui-list-pics mxui-lazy mxui-part-5by2 mxui-part-rows" style="background:url({:mac_url_img($mxprost.mxprocms.s1.userbg)});"> 
      <div class="mxui-part-core mxui-text-center"> 
       <div class="mxui-user-image" data-role="{:mac_url('user/portrait')}"> 
        <img class="face mxui-user-avat mxui-part-roun" src="{$obj.user_portrait|mac_default='static/images/touxiang.png'|mac_url_img}?v={:time()}" /> 
       </div> 
       <span class="mxui-visible mxui-text-white mxui-padding">{$obj.user_name}</span> 
      </div> 
     </div> 
     <div class="mxui-padding mxui-part-rows mxui-back-whits mxui-hide-md"> 
      <ul class="mxui-user-brief mxui-part-rows mxui-back-whits"> 
       <li class="mxui-padding-x mxui-text-center mxui-col-xs4"> <span class="mxui-visible mxui-text-gules">{$obj.user_points}</span> <span class="mxui-visible">我的积分</span> </li> 
       <li class="mxui-padding-x mxui-text-center mxui-line-left mxui-col-xs4"> <span class="mxui-visible mxui-text-gules">{$obj.group.group_name}</span> <span class="mxui-visible">我的等级</span> </li> 
       <li class="mxui-padding-x mxui-text-center mxui-line-left mxui-col-xs4"> <span class="mxui-visible mxui-text-gules">{$obj.user_extend}</span> <span class="mxui-visible">推广次数</span> </li> 
      </ul> 
     </div> 
    </div> 
    <div class="mxui-part-rows"> 
     <div class="mxui-main-left mxui-col-xs12 mxui-col-md4 mxui-col-lg3 mxui-hide-xs mxui-hide-sm mxui-show-md-block">
       {include file="user/menu"} 
     </div> 
     <div class="mxui-main-right mxui-col-xs12 mxui-col-md8 mxui-col-lg9"> 
      <div class="mxui-part-layout mxui-back-whits"> 
       <div class="mxui-user-title mxui-list-head mxui-part-rows mxui-padding mxui-line-bottom"> 
        <h2 class="mxui-font-xvi mxui-padding">提现记录</h2> 
        <ul class="mxui-part-tips mxui-padding"> 
         <li class="mxui-padding"> <a class="mxui-more" href="{:mac_url('user/index')}">返回</a> </li> 
        </ul> 
       </div> 
	   <form id="fm" name="fm" method="post" action="" >
       <ul class="mxui-user-list mxui-part-rows mxui-back-whits">
        <li class="mxui-padding-x mxui-part-rows mxui-line-bottom"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
          <span class="mxui-col-xs12">友情提示：剩余{$GLOBALS['user']['user_points']}积分，1元等于{$GLOBALS['config']['user']['cash_ratio']}积分，相当于{$GLOBALS['user']['user_points']/$GLOBALS['config']['user']['cash_ratio']}元，最低提现金额：{$GLOBALS['config']['user']['cash_min']}元</span> 
         </div> </li> 	   
        <li class="mxui-padding-x mxui-part-rows mxui-line-bottom"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
          <span class="mxui-col-xs4 mxui-col-sm3 mxui-part-eone">银行名称</span> 
          <span class="mxui-col-xs7 mxui-col-sm6 mxui-part-eone"><input type="text" class="mxui-form-control" name="cash_bank_name" value="" placeholder="请输入开户行名称或支付宝微信" /></span> 
          <span class="mxui-col-xs1 mxui-col-sm3 mxui-part-eone mxui-text-right"><i class="mxui-icon-font mxui-icon-you"></i></span> 
         </div> </li> 
        <li class="mxui-padding-x mxui-part-rows mxui-line-bottom"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
          <span class="mxui-col-xs4 mxui-col-sm3 mxui-part-eone">银行账号</span> 
          <span class="mxui-col-xs7 mxui-col-sm6 mxui-part-eone"><input type="text" class="mxui-form-control" name="cash_bank_no" value="" placeholder="请输入银行卡号或支付宝微信账号" /></span> 
          <span class="mxui-col-xs1 mxui-col-sm3 mxui-part-eone mxui-text-right"><i class="mxui-icon-font mxui-icon-you"></i></span> 
         </div> </li>
        <li class="mxui-padding-x mxui-part-rows mxui-line-bottom"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
          <span class="mxui-col-xs4 mxui-col-sm3 mxui-part-eone">收款姓名</span> 
          <span class="mxui-col-xs7 mxui-col-sm6 mxui-part-eone"><input type="text" class="mxui-form-control" name="cash_payee_name" value="" placeholder="请输入收款人姓名与上方账户对应" /></span> 
          <span class="mxui-col-xs1 mxui-col-sm3 mxui-part-eone mxui-text-right"><i class="mxui-icon-font mxui-icon-you"></i></span> 
         </div> </li>
        <li class="mxui-padding-x mxui-part-rows mxui-line-bottom"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
          <span class="mxui-col-xs4 mxui-col-sm3 mxui-part-eone">提现金额</span> 
          <span class="mxui-col-xs7 mxui-col-sm6 mxui-part-eone"><input type="text" class="mxui-form-control" name="cash_money" value="" placeholder="请输入提现金额" /></span> 
          <span class="mxui-col-xs1 mxui-col-sm3 mxui-part-eone mxui-text-right"><i class="mxui-icon-font mxui-icon-you"></i></span> 
         </div> </li>
        <li class="mxui-padding-x mxui-part-rows"><input class="mxui-subm-price mxui-rims-info mxui-btns-info mxui-btns-green mxui-btns" type="button" id="btn_submit" value="提交" /> </li>  
       </ul> 
	   </form>
	   <form class="mxui-user-form mxui-user-info" id="form1" name="form1" method="post">
       <ul class="mxui-user-list mxui-part-rows mxui-back-whits mxui-line-top mxui-vodlist__text to-color" style="margin-top: 30px;"> 
        <li class="mxui-padding-x mxui-part-rows mxui-striped-head"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
          <span class="mxui-col-xs3 mxui-col-sm2 mxui-part-eone">状态</span> 
          <span class="mxui-col-xs4 mxui-col-sm3 mxui-part-eone">提现积分</span> 
          <span class="mxui-col-xs3 mxui-col-sm3 mxui-part-eone">提现金额</span> 
          <span class="mxui-hide-xs mxui-col-sm3 mxui-part-eone">时间</span> 
          <span class="mxui-col-xs2 mxui-col-sm1 mxui-part-eone">操作</span> 
         </div> </li> {volist name="list" id="vo"} 
        <li class="mxui-padding-x mxui-part-rows mxui-line-top"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows mxui-text-center"> 
          <span class="mxui-col-xs3 mxui-col-sm2 mxui-part-eone">{if condition="$vo.cash_status eq '1'"}已审核{else/}未审核{/if}</span> 
          <span class="mxui-col-xs4 mxui-col-sm3 mxui-part-eone">{$vo.cash_points}</span> 
          <span class="mxui-col-xs3 mxui-col-sm3 mxui-part-eone">{$vo.cash_money}</span>  
          <span class="mxui-hide-xs mxui-col-sm3 mxui-part-eone">{$vo.cash_time|mac_day}</span> 
          <span class="delete mxui-col-xs2 mxui-col-sm1 mxui-part-eone"><a href="javascript:;" onclick="delData({$vo.cash_id},0)">删除</a></span> 
         </div> </li> {/volist} {if$__PAGING__.page_total>1} 
        <div class="mxui-page-info mxui-text-center"> 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-xs-inline{if$__PAGING__['page_current']==1} mxui-btns-disad{/if}" href="{$__PAGING__.page_url|mac_url_page=1}">首页</a> 
         <a class="mxui-btns-info mxui-rims-info{if$__PAGING__['page_current']==1} mxui-btns-disad{/if}" href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_prev}">上一页</a> {if$__PAGING__.page_current>3} 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline" href="{$__PAGING__.page_url|mac_url_page=1}">1</a> 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline mxui-btns-disad" href="javascript:;">...</a> {/if} {maccms:foreach name="$__PAGING__.page_num" id="num"} 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline{if$__PAGING__['page_current']==$num} mxui-btns-green{/if}" href="{if$__PAGING__['page_current']==$num}javascript:;{else}{$__PAGING__.page_url|mac_url_page=$num}{/if}">{$num}</a> {/maccms:foreach} {if$__PAGING__.page_current<($__PAGING__.page_total-2)} 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline mxui-btns-disad" href="javascript:;">...</a> 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline" href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_total}">{$__PAGING__.page_total}</a> {/if} 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-xs-inline" href="javascript:;">{$__PAGING__.page_current}/{$__PAGING__.page_total}</a> 
         <a class="mxui-btns-info mxui-rims-info{if$__PAGING__['page_current']==$__PAGING__.page_total} mxui-btns-disad{/if}" href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_next}">下一页</a> 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-xs-inline{if$__PAGING__['page_current']==$__PAGING__.page_total} mxui-btns-disad{/if}" href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_total}">尾页</a> 
        </div> {/if}{if$__PAGING__.record_total!=0} 
        <script type="text/javascript">
		if(document.getElementById('mxui-now')) document.getElementById('mxui-now').innerHTML='{$__PAGING__.page_current}';
		if(document.getElementById('mxui-count')) document.getElementById('mxui-count').innerHTML='{$__PAGING__.record_total}';
		</script> {/if} 
       </ul> 
	   </form>
      </div> 
     </div> 
    </div> 
   </div> 
  </div> 
  {include file="public/foot"}
  <script>
	function delData(ids,all){
		var msg ='删除';
		if(all==1){
			msg='清空';
		}
		if(confirm('确定要'+msg+'记录吗')){
			$.post("{:url('user/cash_del')}",{ids:ids,all:all},function(data) {
				if (data.code == '1') {
					layer.msg('删除成功');
					location.reload();
				}else {
					layer.msg('删除失败：' + data.msg);
				}
			}, 'json')
		}
	}
	$("#btnClear").click(function(){
		delData('',1);
	});
	$("#btnDel").click(function(){
		var ids = MAC.CheckBox.Ids('ids[]');
		if(ids==''){
			layer.msg("请至少选择一个数据");
			return;
		}
		delData(ids,0);
	});
    $("#btn_submit").click(function() {
        var cash_bank_name = $('input[name="cash_bank_name"]').val();
        if(cash_bank_name==''){
            layer.msg('请输入银行名称');
            return;
        }
        var cash_bank_no = $('input[name="cash_bank_no"]').val();
        if(cash_bank_no==''){
            layer.msg('请输入银行账户');
            return;
        }
        var cash_payee_name = $('input[name="cash_payee_name"]').val();
        if(cash_payee_name==''){
            layer.msg('请输入收款人姓名');
            return;
        }
        var cash_money = $('input[name="cash_money"]').val();
        if(cash_money==''){
            layer.msg('请输入提现金额');
            return;
        }

        var data = $("#fm").serialize();
        $.ajax({
            url: "{:url('user/cash')}",
            type: "post",
            dataType: "json",
            data: data,
            beforeSend: function () {
                //开启loading
                //$(".loading_box").css("display","block");
                $("#btn_submit").val("loading...");
            },
            success: function (r) {
                layer.msg(r.msg);
                if(r.code==1){
                    location.href="{:url('user/cash')}";
                }
            },
            complete: function () {
                //结束loading
                //$(".loading_box").css("display","none");
                $("#btn_submit").val("提交");
            }
        });
    });

	$(".face").imageUpload({
		formAction: "{:url('user/portrait')}",
		inputFileName:'file',
		browseButtonValue: '',
		browseButtonClass:'btn btn-default btn-xs mxui-user-alter mxui-part-roun mxui-icon-font icon-xiugai',
		automaticUpload: true,
		hideDeleteButton: true,
		hover:true
	})
	$(".jQuery-image-upload-controls").mouseenter(function(){
    $(".jQuery-image-upload-controls").css("display","block");
	});
	$(".jQuery-image-upload-controls").mouseleave(function(){
    $(".jQuery-image-upload-controls").css("display","none");
	});
	$(".face").on("imageUpload.uploadFailed", function (ev, err) {
		layer.msg(err);
	});
</script>  
 </body>
</html>