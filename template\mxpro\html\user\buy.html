<!DOCTYPE html>
<html>
 <head> 
  	{include file="public/include"}
  <title>充值中心 - 个人中心 - {$maccms.site_name}</title> 
  <meta name="keywords" content="个人中心,{$maccms.site_keywords}" /> 
  <meta name="description" content="{$maccms.site_description}" />
  {include file="user/include"}   
 </head>
 <body class="mxui-min-width">
   <div class="page list">
      {include file="public/head"} 
   <div class="mxui-part-case  main"> 
    <div class="mxui-user-head mxui-margin-top mxui-back-whits"> 
     <div class="mxui-list-pics mxui-lazy mxui-part-5by2 mxui-part-rows" style="background:url({:mac_url_img($mxprost.mxprocms.s1.userbg)});"> 
      <div class="mxui-part-core mxui-text-center"> 
       <div class="mxui-user-image" data-role="{:mac_url('user/portrait')}"> 
        <img class="face mxui-user-avat mxui-part-roun" src="{$obj.user_portrait|mac_default='static/images/touxiang.png'|mac_url_img}?v={:time()}" /> 
       </div> 
       <span class="mxui-visible mxui-text-white mxui-padding">{$obj.user_name}</span> 
      </div> 
     </div> 
     <div class="mxui-padding mxui-part-rows mxui-back-whits mxui-hide-md"> 
      <ul class="mxui-user-brief mxui-part-rows mxui-back-whits"> 
       <li class="mxui-padding-x mxui-text-center mxui-col-xs4"> <span class="mxui-visible mxui-text-gules">{$obj.user_points}</span> <span class="mxui-visible">我的积分</span> </li> 
       <li class="mxui-padding-x mxui-text-center mxui-line-left mxui-col-xs4"> <span class="mxui-visible mxui-text-gules">{$obj.group.group_name}</span> <span class="mxui-visible">我的等级</span> </li> 
       <li class="mxui-padding-x mxui-text-center mxui-line-left mxui-col-xs4"> <span class="mxui-visible mxui-text-gules">{$obj.user_login_num}</span> <span class="mxui-visible">登录次数</span> </li> 
      </ul> 
     </div> 
    </div> 
    <div class="mxui-part-rows"> 
     <div class="mxui-main-left mxui-col-xs12 mxui-col-md4 mxui-col-lg3 mxui-hide-xs mxui-hide-sm mxui-show-md-block">
       {include file="user/menu"} 
     </div> 
     <div class="mxui-main-right mxui-col-xs12 mxui-col-md8 mxui-col-lg9"> 
      <div class="mxui-part-layout mxui-back-whits"> 
       <div class="mxui-user-title mxui-list-head mxui-part-rows mxui-padding mxui-line-bottom"> 
        <h2 class="mxui-font-xvi mxui-padding">在线充值</h2> 
        <ul class="mxui-part-tips mxui-padding"> 
         <li class="mxui-padding"> <a class="mxui-more" href="{:mac_url('user/index')}">返回</a> </li> 
        </ul> 
       </div> 
       <ul class="mxui-user-list mxui-part-rows mxui-back-whits"> 
        <li class="mxui-padding-x mxui-part-rows mxui-line-bottom"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
          <span class="mxui-col-xs4 mxui-col-sm3 mxui-part-eone">充值金额</span> 
          <span class="mxui-col-xs7 mxui-col-sm6 mxui-part-eone"><input type="text" name="price" value="{$config.min}" class="mxui-form-control" placeholder="充值金额" /></span> 
          <span class="mxui-col-xs1 mxui-col-sm3 mxui-part-eone mxui-text-right"><i class="mxui-icon-font mxui-icon-you"></i></span> 
         </div> </li> 
        <li class="mxui-padding-x mxui-part-rows mxui-line-bottom"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
          <span class="mxui-col-xs12 mxui-part-eone">友情提示：最小充值金额为{$config.min}元，1元={$config.scale}积分</span> 
         </div> </li> 
        <li class="mxui-padding-x mxui-part-rows"><input class="mxui-subm-price  mxui-rims-info mxui-btns-info mxui-btns-green  mxui-btns" type="button" id="btn_submit_pay" value="确认" /> </li> 
        <div class="mxui-user-title mxui-list-head mxui-part-rows mxui-padding mxui-line-bottom"> 
          <h2 class="mxui-font-xvi mxui-padding">充值卡充值</h2> 
         </div> 
        <li class="mxui-padding-x mxui-part-rows mxui-line-bottom"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
          <span class="mxui-col-xs4 mxui-col-sm3 mxui-part-eone">充值卡号</span> 
          <span class="mxui-col-xs7 mxui-col-sm6 mxui-part-eone"><input type="text" class="mxui-form-control" name="card_no" maxlength="40" placeholder="充值卡号" /></span> 
          <span class="mxui-col-xs1 mxui-col-sm3 mxui-part-eone mxui-text-right"><i class="mxui-icon-font mxui-icon-you"></i></span> 
         </div> 
         </li> 
            <li class="mxui-padding-x mxui-part-rows mxui-line-bottom"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
          <span class="mxui-col-xs4 mxui-col-sm3 mxui-part-eone">充值密码</span> 
          <span class="mxui-col-xs7 mxui-col-sm6 mxui-part-eone"><input type="text" class="mxui-form-control" name="card_pwd" maxlength="40" placeholder="充值密码" /></span> 
          <span class="mxui-col-xs1 mxui-col-sm3 mxui-part-eone mxui-text-right"><i class="mxui-icon-font mxui-icon-you"></i></span> 
         </div>
         </li> 
         <li class="mxui-padding-x mxui-part-rows mxui-line-bottom"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
          <span class="mxui-col-xs12 mxui-part-eone">友情提示：请到卡密平台购买充值卡</span> 
         </div> </li> 
        <li class="mxui-padding-x mxui-part-rows"> <input class="mxui-subm-cards mxui-rims-info mxui-btns-info mxui-btns-green  mxui-btns" type="button" id="btn_submit_card" value="确认" /> </li> 
       </ul> 
      </div> 
     </div> 
    </div> 
   </div> 
  </div> 
  {include file="public/foot"}
  <script>
	$(".go-back").click(function () {
		var ref = document.referrer;
		location.href=ref;
	});

	$('#btn_submit_pay').click(function(){
		var that=$(this);
		var price = $("input[name='price']").val();
		if(Number(price)<1){
			return;
		}
		if(confirm('确定要在线充值吗')) {
			$.ajax({
				url: "{:url('user/buy')}",
				type: "post",
				dataType: "json",
				data: {price: price,flag:'pay'},
				beforeSend: function () {
					$("#btn_submit_pay").val("loading...");
				},
				success: function (r) {
					if (r.code == 1) {
						location.href="{:url('user/pay')}?order_code=" + r.data.order_code;
					}
					else{
						layer.msg(r.msg);
					}
				},
				complete: function () {
					$("#btn_submit_pay").val("提交");
				}
			});
		}
	});

		$('#btn_submit_card').click(function(){
		var that=$(this);
		var no = $('input[name="card_no"]').val();
		var pwd = $('input[name="card_pwd"]').val();
		if(no=='' || pwd==''){
			layer.msg('请输入充值卡号和密码');
			return;
		}
		if(confirm('确定要使用充值卡充值吗')) {
			$.ajax({
				url: "{:url('user/buy')}",
				type: "post",
				dataType: "json",
				data: {card_no: no,card_pwd:pwd,flag:'card'},
				beforeSend: function () {
					$("#btn_submit_card").css("background","#fd6a6a").val("loading...");
				},
				success: function (r) {
					layer.msg(r.msg);
				},
				complete: function () {
					$("#btn_submit_card").css("background","#fa4646").val("提交");
				}
			});
		}
	});

	$(".face").imageUpload({
		formAction: "{:url('user/portrait')}",
		inputFileName:'file',
		browseButtonValue: '',
		browseButtonClass:'btn btn-default btn-xs mxui-user-alter mxui-part-roun mxui-icon-font icon-xiugai',
		automaticUpload: true,
		hideDeleteButton: true,
		hover:true
	})
	$(".jQuery-image-upload-controls").mouseenter(function(){
    $(".jQuery-image-upload-controls").css("display","block");
	});
	$(".jQuery-image-upload-controls").mouseleave(function(){
    $(".jQuery-image-upload-controls").css("display","none");
	});
	$(".face").on("imageUpload.uploadFailed", function (ev, err) {
		layer.msg(err);
	});
</script>  
 </body>
</html>