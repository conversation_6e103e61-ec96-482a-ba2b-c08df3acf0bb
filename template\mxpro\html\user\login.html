<!DOCTYPE html>
<html>
 <head> 
    {include file="public/include"}
  <title>会员登录 - 个人中心 - {$maccms.site_name}</title> 
  <meta name="keywords" content="个人中心,{$maccms.site_keywords}" /> 
  <meta name="description" content="{$maccms.site_description}" />
  {include file="user/include"}
 </head>
 <body class="mxui-min-width">
<div class="page list">
      {include file="public/head"}    
   <div class="mxui-part-case mxui-part-margin main"> 
    <div class="mxui-back-whits mxui-part-height"> 
     <div class="mxui-user-login mxui-part-core-bg"> 
      <div class="mxui-list-head mxui-part-rows mxui-padding"> 
       <h2 class="mxui-font-xvii mxui-text-center">会员登录</h2> 
      </div> 
      <form class="mxui-user-form mxui-user-width mxui-part-rows" action="" method="post" id="fm"> 
       <input class="mxui-user-text mxui-col-xs12" type="text" id="user_name" name="user_name" placeholder="请输入账号" maxlength="30" /> 
       <input class="mxui-user-text mxui-col-xs12" type="password" id="user_pwd" name="user_pwd" placeholder="请输入密码" maxlength="20" /> {if condition="$GLOBALS['config']['user']['login_verify'] eq 1"} 
       <input class="mxui-user-text mxui-col-xs5" type="tel" id="verify" name="verify" placeholder="请输入验证码" maxlength="4" /> 
       <img class="mxui-user-code mxui-user-text mxui-col-xs4" height="45" src="{:mac_url('verify/index')}" id="verify_img" onclick="this.src=this.src+'?'" title="看不清楚? 换一张！" /> {/if} 
       <input type="button" id="btn_submit" class="mxui-subm-login mxui-rims-info mxui-btns-info mxui-btns-gules mxui-col-xs12 m20" value="登录" /> 
       <a class="mxui-col-xs3 mxui-text-left" href="{:mac_url('user/reg')}">注册账号</a> {if condition="$GLOBALS['config']['connect']['qq']['status'] eq 1"} 
       <a class="mxui-col-xs3 mxui-text-center" href="{:mac_url('user/oauth')}?type=qq">QQ登录</a> {/if} {if condition="$GLOBALS['config']['connect']['weixin']['status'] eq 1"} 
       <a class="mxui-col-xs3 mxui-text-center" href="{:mac_url('user/oauth')}?type=weixin">微信登录</a> {/if} 
       <a class="mxui-text-right" style="float:right" href="{:mac_url('user/findpass_msg')}?ac=email">忘记密码</a> 
      </form> 
     </div> 
    </div> 
   </div> 
  </div> 
  {include file="public/foot"}
  <script type="text/javascript">

	$(function(){
		$("body").bind('keyup',function(event) {
			if(event.keyCode==13){ $('#btnLogin').click(); }
		});
		$('#btn_submit').click(function() {
			if ($('#user_name').val()  == '') { layer.msg('请输入用户！'); $("#user_name").focus(); return false; }
			if ($('#user_pwd').val()  == '') { layer.msg('请输入密码！'); $("#user_pwd").focus(); return false; }
			if ($('#verify').length> 0 && $('#verify').val()  == '') { layer.msg('请输入验证码！'); $("#verify").focus(); return false; }

			$.ajax({
				url: "{:mac_url('user/login')}",
				type: "post",
				dataType: "json",
				data: $('#fm').serialize(),
				beforeSend: function () {
					$("#btn_submit").val("loading...");
				},
				success: function (r) {
					if(r.code==1){
						location.href="{:mac_url('user/index')}";
					}
					else{
						layer.msg(r.msg);
						$('#verify_img').click();
					}
				},
				complete: function () {
					$("#btn_submit").val("立即登录");
				}
			});

		});
	});

</script>  
 </body>
</html>