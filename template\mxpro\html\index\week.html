<?php
      function m_day($t)
         {
            if(empty($t)) { return ''; }
            if(is_numeric($t)){
               $t = date('Y-m-d H:i:s',$t);
            }
            $now = date('Y-m-d',time());
            if(strpos(','.$t,$now)>0){
               return 1;
            }
            return  0;
         }
         $week=explode(',',$GLOBALS['config']['app']['vod_extend_weekday']);
         $ws = date("w");
         if($ws == 0){
            $ws = 7;
         };
         $weekarray=$week[$ws-1];
      ?>

	<div class="module">
		<div class="module-heading m-module-heading">
			<h2 class="module-title m-module-title">{$mxprost.mxprocms.s3.indexweektext}</h2>
			<div class="module-tab m-module-tab">
				<div class="module-tab-week m-module-tab-week">
					{maccms:foreach name="$week" id="vo" key="key"}
					<span class="module-tab-item m-tab-item tab-item {if $weekarray==$vo}active{/if}" data-dropdown-value="周{$vo}">周{$vo}</span> {/maccms:foreach}
				</div>
			</div>
		</div>
		{maccms:foreach name=":explode(',',$maccms.vod_extend_weekday)" id="vo1" key="key1"}
		<div class="module-main tab-list {if condition="$mxprost['mxprocms']['s2']['qjpic'] eq 1"}scroll-box{/if} {if $weekarray==$vo1}active{/if}">
			<div class="module-items {if condition="$mxprost['mxprocms']['s2']['qjpic'] eq 1"}module-poster-items-small scroll-content {else}module-poster-items-base {/if}">
				{maccms:vod type="all" weekday="'.$vo1.'" order="desc" by="time" num="'.$mxprost['mxprocms']['s3']['indexweeknum'].'"}
				 {include file="block/weekbox"}
				{/maccms:vod}
			</div>
		</div>
		{/maccms:foreach}
	</div>