<!DOCTYPE html>
<html>
 <head> 
  	{include file="public/include"}
  <title>订单记录 - 个人中心 - {$maccms.site_name}</title> 
  <meta name="keywords" content="个人中心,{$maccms.site_keywords}" /> 
  <meta name="description" content="{$maccms.site_description}" />
  {include file="user/include"}
 </head>
 <body class="mxui-min-width">
 <div class="page list">
      {include file="public/head"} 
   <div class="mxui-part-case main"> 
    <div class="mxui-user-head mxui-margin-top mxui-back-whits"> 
     <div class="mxui-list-pics mxui-lazy mxui-part-5by2 mxui-part-rows" style="background:url({:mac_url_img($mxprost.mxprocms.s1.userbg)});"> 
      <div class="mxui-part-core mxui-text-center"> 
       <div class="mxui-user-image" data-role="{:mac_url('user/portrait')}"> 
        <img class="face mxui-user-avat mxui-part-roun" src="{$obj.user_portrait|mac_default='static/images/touxiang.png'|mac_url_img}?v={:time()}" /> 
       </div> 
       <span class="mxui-visible mxui-text-white mxui-padding">{$obj.user_name}</span> 
      </div> 
     </div> 
     <div class="mxui-padding mxui-part-rows mxui-back-whits mxui-hide-md"> 
      <ul class="mxui-user-brief mxui-part-rows mxui-back-whits"> 
       <li class="mxui-padding-x mxui-text-center mxui-col-xs4"> <span class="mxui-visible mxui-text-gules">{$obj.user_points}</span> <span class="mxui-visible">我的积分</span> </li> 
       <li class="mxui-padding-x mxui-text-center mxui-line-left mxui-col-xs4"> <span class="mxui-visible mxui-text-gules">{$obj.group.group_name}</span> <span class="mxui-visible">我的等级</span> </li> 
       <li class="mxui-padding-x mxui-text-center mxui-line-left mxui-col-xs4"> <span class="mxui-visible mxui-text-gules">{$obj.user_login_num}</span> <span class="mxui-visible">登录次数</span> </li> 
      </ul> 
     </div> 
    </div> 
    <div class="mxui-part-rows"> 
     <div class="mxui-main-left mxui-col-xs12 mxui-col-md4 mxui-col-lg3 mxui-hide-xs mxui-hide-sm mxui-show-md-block">
       {include file="user/menu"} 
     </div> 
     <div class="mxui-main-right mxui-col-xs12 mxui-col-md8 mxui-col-lg9"> 
      <div class="mxui-part-layout mxui-back-whits"> 
       <div class="mxui-user-title mxui-list-head mxui-part-rows mxui-padding mxui-line-bottom"> 
        <h2 class="mxui-font-xvi mxui-padding"><a class="mxui-more" href="{:mac_url('user/orders')}">在线充值记录</a>&nbsp;&nbsp;<a class="mxui-text-green mxui-more" href="{:mac_url('user/cards')}">充值卡记录</a></h2> 
        <ul class="mxui-part-tips mxui-padding"> 
         <li class="mxui-padding"> <a class="mxui-more" href="{:mac_url('user/index')}">返回</a> </li> 
        </ul> 
       </div> 
       <ul class="mxui-user-list mxui-part-rows mxui-back-whits mxui-vodlist__text to-color"> 
        <li class="mxui-padding-x mxui-part-rows mxui-striped-head"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows"> 
          <span class="mxui-col-xs2 mxui-col-sm1 mxui-part-eone">编号</span> 
          <span class="mxui-col-xs6 mxui-col-sm5 mxui-part-eone">卡号</span> 
          <span class="mxui-col-xs2 mxui-col-sm1 mxui-part-eone">面值</span> 
          <span class="mxui-col-xs2 mxui-col-sm1 mxui-part-eone">积分</span> 
          <span class="mxui-hide-xs mxui-col-sm4 mxui-part-eone">时间</span> 
         </div> </li> {volist name="list" id="vo"} 
        <li class="mxui-padding-x mxui-part-rows mxui-line-top"> 
         <div class="mxui-user-input mxui-visible mxui-font-xvi mxui-part-rows mxui-text-center"> 
          <span class="mxui-col-xs2 mxui-col-sm1 mxui-part-eone mxui-text-muted">{$vo.card_id}</span> 
          <span class="mxui-col-xs6 mxui-col-sm5 mxui-part-eone">{$vo.card_no}</span> 
          <span class="mxui-col-xs2 mxui-col-sm1 mxui-part-eone">{$vo.card_money}</span> 
          <span class="mxui-col-xs2 mxui-col-sm1 mxui-part-eone">{$vo.card_points}</span> 
          <span class="mxui-hide-xs mxui-col-sm4 mxui-part-eone">{$vo.card_use_time|mac_day}</span> 
         </div> </li> {/volist} {if$__PAGING__.page_total>1} 
        <div class="mxui-page-info mxui-text-center"> 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-xs-inline{if$__PAGING__['page_current']==1} mxui-btns-disad{/if}" href="{$__PAGING__.page_url|mac_url_page=1}">首页</a> 
         <a class="mxui-btns-info mxui-rims-info{if$__PAGING__['page_current']==1} mxui-btns-disad{/if}" href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_prev}">上一页</a> {if$__PAGING__.page_current>3} 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline" href="{$__PAGING__.page_url|mac_url_page=1}">1</a> 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline mxui-btns-disad" href="javascript:;">...</a> {/if} {maccms:foreach name="$__PAGING__.page_num" id="num"} 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline{if$__PAGING__['page_current']==$num} mxui-btns-green{/if}" href="{if$__PAGING__['page_current']==$num}javascript:;{else}{$__PAGING__.page_url|mac_url_page=$num}{/if}">{$num}</a> {/maccms:foreach} {if$__PAGING__.page_current<($__PAGING__.page_total-2)} 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline mxui-btns-disad" href="javascript:;">...</a> 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-sm-inline" href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_total}">{$__PAGING__.page_total}</a> {/if} 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-xs-inline" href="javascript:;">{$__PAGING__.page_current}/{$__PAGING__.page_total}</a> 
         <a class="mxui-btns-info mxui-rims-info{if$__PAGING__['page_current']==$__PAGING__.page_total} mxui-btns-disad{/if}" href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_next}">下一页</a> 
         <a class="mxui-btns-info mxui-rims-info mxui-hide mxui-show-xs-inline{if$__PAGING__['page_current']==$__PAGING__.page_total} mxui-btns-disad{/if}" href="{$__PAGING__.page_url|mac_url_page=$__PAGING__.page_total}">尾页</a> 
        </div> {/if}{if$__PAGING__.record_total!=0} 
        <script type="text/javascript">
		if(document.getElementById('mxui-now')) document.getElementById('mxui-now').innerHTML='{$__PAGING__.page_current}';
		if(document.getElementById('mxui-count')) document.getElementById('mxui-count').innerHTML='{$__PAGING__.record_total}';
		</script> {/if} 
       </ul> 
      </div> 
     </div> 
    </div> 
   </div> 
  </div> 
  {include file="public/foot"}
  <script>
	$(".face").imageUpload({
		formAction: "{:url('user/portrait')}",
		inputFileName:'file',
		browseButtonValue: '',
		browseButtonClass:'btn btn-default btn-xs mxui-user-alter mxui-part-roun mxui-icon-font icon-xiugai',
		automaticUpload: true,
		hideDeleteButton: true,
		hover:true
	})
	$(".jQuery-image-upload-controls").mouseenter(function(){
    $(".jQuery-image-upload-controls").css("display","block");
	});
	$(".jQuery-image-upload-controls").mouseleave(function(){
    $(".jQuery-image-upload-controls").css("display","none");
	});
	$(".face").on("imageUpload.uploadFailed", function (ev, err) {
		layer.msg(err);
	});
</script>  
 </body>
</html>