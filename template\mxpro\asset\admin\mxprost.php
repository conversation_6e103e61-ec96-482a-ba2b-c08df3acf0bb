<?php

return array(
	'mxprocms' => array(
		's1' => array(
			'logo1' => 'mxtheme/images/logo.png', 
			'logo2' => 'mxtheme/images/logo_black.png', 
			'ico' => 'mxtheme/images/favicon.png', 
			'pic' => 'mxtheme/images/load.gif', 
			'searchwd' => '搜索电影、电视剧、综艺、动漫', 
			'sm' => '本站所有内容均来自互联网分享站点所提供的公开引用资源，未提供资源上传、存储服务'), 
		's2' => array('target' => '0', 
			'navtheme' => '0', 
			'theme' => 'white', 
			'week' => '1', 
			'tzzt' => '0', 
			'douban' => '1', 
			'qjpic' => '0', 
			'dbmap' => '1', 
			'qqqun' => '1', 
			'qqquntext' => 'QQ交流群', 
			'qqqunurl' => '', 
			'dbfk' => '1', 
			'dbfktext' => '底部反馈', 
			'dbfkurl' => '', 
			'telegramtext' => 'Telegram群', 
			'telegram' => '1', 
			'telegramurl' => '', 
			'daohangid' => 'parent', 
			'num1' => '1', 
			'num2' => '2', 
			'num3' => '3', 
			'num4' => '4', 
			'num5' => '', 
			'num6' => '', 
			'num7' => '', 
			'icon1' => 'icon-dy-o', 
			'icon2' => 'icon-tv-o', 
			'icon3' => 'icon-zy-o', 
			'icon4' => 'icon-dm-o', 
			'icon5' => '', 
			'icon6' => '', 
			'icon7' => '', 
			'iconmr' => 'icon-jl-o', 
			'diy1' => '0', 
			'diy1name' => '', 
			'diy1url' => '', 
			'diy1icon' => 'icon-diy', 
			'diy2' => '0', 
			'diy2name' => '', 
			'diy2url' => '', 
			'diy2icon' => 'icon-diy', 
			'app' => '1', 
			'appname' => 'APP', 
			'appurl' => '/index.php/label/down.html', 
			'appicon' => 'icon-phone-o', 
			'todaynew' => '1', 
			'navhot' => '1', 
			'themekg' => '1', 
			'zongkg' => '1', 
			'up' => '1', 
			'gbook' => '1',
			'searchtips' => '1',
			'diyheaddm' => '0',
			'diy2headdm' => '0'
		), 
		's3' => array(
			'slidebill' => '1',
			'slide' => '1', 
			'slideby' => 'time', 
			'slidenum' => '10', 
			'slidetj' => '5', 
			'slideorder' => 'asc', 
			'slidefb' => '1', 
			'indexweek' => '1', 
			'indexweektext' => '追剧周表', 
			'indexweeknum' => '', 
			'indexhot' => '1', 
			'indexhottj' => '1', 
			'indexhotnum' => '16', 
			'indexhottext' => '正在热映', 
			'indexhotpic' => '1', 
			'indexlist' => '1', 
			'indexlistid' => 'parent', 
			'indexlistnum1' => '16', 
			'indexlistby1' => 'time', 
			'indexlistorder1' => 'asc', 
			'indexlistyear1' => '2022', 
			'indextop' => '1', 
			'indextopid' => 'parent', 
			'indextopnum' => '10', 
			'indextopby' => 'hits', 
			'indextoporder' => 'asc', 
			'indextopyear' => '2022', 
			'indextoptext' => '热榜', 
			'indextopeg' => 'RANKING', 
			'indexlink' => '1', 
			'indexlinknum' => '99'
		), 's4' => array(
			'likenum' => '16', 
			'likeyear' => '2022', 
			'liketext' => '相关推荐', 
			'sort' => '1', 'report' => '1', 
			'share' => '1', 'phone' => '1', 
			'qrcodetips' => '被部分浏览器误屏蔽，为保证正常访问，推荐使用<strong>Chrome</strong>、<strong>Edge</strong>、<strong>Safari</strong>等浏览器。 ', 
			'type' => '1', 
			'typenum' => '10', 
			'typelevel' => '0', 
			'typeyear' => '2022', 
			'typeby' => 'time', 
			'type1' => '1', 
			'typenum1' => '16', 
			'typeyear1' => '2022', 
			'typeby1' => 'time', 
			'typetext' => '新片上线', 
			'typetexteg' => 'NEW FILM ONLINE', 
			'typestart' => '9', 
			'type2' => '1', 
			'typenum2' => '16', 
			'typeyear2' => '', 
			'typetext1' => '排行榜', 
			'typetexteg1' => 'RANKING', 
			'type3' => '1', 
			'typenum3' => '16', 
			'typeyear3' => '', 
			'typetext2' => '最近更新', 
			'typetexteg2' => 'LATEST UPDATE', 
			'typeby3' => 'time', 
			'showclass' => '1', 
			'showjq' => '1', 
			'showarea' => '1', 
			'showyear' => '1', 
			'showlang' => '1', 
			'showzm' => '1', 
			'showby' => '1', 
			'newtext' => '今日更新', 
			'newtext1' => '新片上线', 
			'newnum' => '48', 
			'newby' => 'time', 
			'neworder' => 'asc', 
			'newyear' => '', 
			'newnum1' => '48', 
			'newby1' => 'time', 
			'neworder1' => 'asc', 
			'newyear1' => '2022', 
			'hottext' => '最近热门', 
			'hottext1' => '近期热门', 
			'hotnum' => '48', 
			'hotby' => 'hits', 
			'hotorder' => 'asc', 
			'hotyear' => '', 
			'hotnum1' => '48', 
			'hotby1' => 'hits', 
			'hotorder1' => 'asc', 
			'hotyear1' => '2022', 
			'copyright' => '0', 
			'copyrighttxt' => '应版权方要求该资源已下架', 
			'apptxt' => 'Mxone Pro网站APP', 
			'appentxt' => '海量高清影片、更佳观影体验，尽在Mxone Pro APP', 
			'android' => '0', 
			'ios' => '0', 
			'win' => '0', 
			'androidurl' => '', 
			'iosurl' => '', 
			'winurl' => '', 
			'androidicon' => 'icon-android', 
			'iosicon' => 'icon-apple', 
			'winicon' => 'icon-windows', 
			'androidtxt' => 'Android版', 
			'iostxt' => 'ios版', 
			'wintxt' => '客户端版', 
			'appewm' => 'mxtheme/images/ewm.png'
		), 
		's5' => array(
			'notice' => '1', 
			'noticetext' => '公告内容', 
			'noticecontent' => '<strong>欢迎使用Mxone Pro 2.0模板', 
			'shier' => '0', 
			'pbtips' => '你知道的太多了', 
			'ctrl' => '0', 
			'right' => '0', 
			'mode' => '0', 
			'tbdmtips' => '', 
			'dbdmtips' => '统计代码', 
			'stylecss' => ''
		), 
		's6' => array(
			'adsindex' => '0', 
			'adsindexdm' => '', 
			'adsindexlist' => '0', 
			'adsindexlistdm' => '', 
			'adsdetdm' => '', 
			'adsdet' => '0', 
			'adsplaydm' => '', 
			'adsplay' => '0', 
			'adsplaybfqdm' => '', 
			'adsplaybfq' => '0', 
			'adssearchdm' => '', 
			'adssearch' => '', 
			'adsscreendm' => '', 
			'adsscreen' => '0'
		)
	)
);